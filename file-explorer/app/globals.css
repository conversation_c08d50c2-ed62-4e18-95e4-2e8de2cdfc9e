@tailwind base;
@tailwind components;
@tailwind utilities;



/* Icon Standardization - Modern Monochrome Style */
.icon {
  width: 20px;
  height: 20px;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.icon-sm {
  width: 16px;
  height: 16px;
}

.icon-xs {
  width: 14px;
  height: 14px;
}

.icon-lg {
  width: 24px;
  height: 24px;
}

.icon-xl {
  width: 32px;
  height: 32px;
}

/* Activity Bar Icons */
.activity-bar-icon {
  width: 20px;
  height: 20px;
  stroke: currentColor;
  fill: none;
  stroke-width: 1.5;
  stroke-linecap: round;
  stroke-linejoin: round;
  transition: all 0.2s ease;
}

/* Sidebar Icons - Slightly smaller and more subtle */
.sidebar-icon {
  width: 14px;
  height: 14px;
  stroke: currentColor;
  fill: none;
  stroke-width: 1.5;
  stroke-linecap: round;
  stroke-linejoin: round;
  opacity: 0.8;
}

/* File Type Icons - Smaller and more subtle */
.file-icon {
  width: 12px;
  height: 12px;
  stroke: currentColor;
  fill: none;
  stroke-width: 1.5;
  stroke-linecap: round;
  stroke-linejoin: round;
  opacity: 0.7;
}

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;

    --radius: 0.5rem;

    /* Editor specific colors - Softer light theme */
    --editor-bg: 210 20% 98%;
    --editor-fg: 215 25% 27%;
    --editor-border: 220 13% 91%;
    --editor-sidebar-bg: 210 20% 96%;
    --editor-sidebar-fg: 215 25% 27%;
    --editor-activity-bar-bg: 210 20% 93%;
    --editor-activity-bar-fg: 215 25% 27%;
    --editor-tab-bg: 210 20% 98%;
    --editor-tab-fg: 215 25% 27%;
    --editor-tab-active-bg: 0 0% 100%;
    --editor-tab-active-fg: 215 25% 27%;
    --editor-terminal-bg: 210 20% 96%;
    --editor-terminal-fg: 215 25% 27%;
    --editor-statusbar-bg: 210 20% 93%;
    --editor-statusbar-fg: 215 25% 27%;
    --editor-highlight: 262 83.3% 57.8%;
    --editor-highlight-fg: 0 0% 100%;
  }

  .dark {
    --background: 224 25% 12%;
    --foreground: 210 20% 98%;

    --card: 224 25% 12%;
    --card-foreground: 210 20% 98%;

    --popover: 224 25% 12%;
    --popover-foreground: 210 20% 98%;

    --primary: 210 20% 98%;
    --primary-foreground: 220 25% 12%;

    --secondary: 215 27% 16.5%;
    --secondary-foreground: 210 20% 98%;

    --muted: 215 27% 16.5%;
    --muted-foreground: 217 15% 65%;

    --accent: 215 27% 16.5%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;

    --border: 215 27% 16.5%;
    --input: 215 27% 16.5%;
    --ring: 216 12% 84%;

    /* Editor specific colors - Subtle dark theme with navy/slate undertones */
    --editor-bg: 224 25% 12%;
    --editor-fg: 210 20% 98%;
    --editor-border: 215 27% 16.5%;
    --editor-sidebar-bg: 222 28% 10%;
    --editor-sidebar-fg: 210 20% 98%;
    --editor-activity-bar-bg: 225 30% 8%;
    --editor-activity-bar-fg: 210 20% 98%;
    --editor-tab-bg: 224 25% 12%;
    --editor-tab-fg: 210 20% 98%;
    --editor-tab-active-bg: 222 28% 15%;
    --editor-tab-active-fg: 210 20% 98%;
    --editor-terminal-bg: 222 28% 10%;
    --editor-terminal-fg: 210 20% 98%;
    --editor-statusbar-bg: 225 30% 8%;
    --editor-statusbar-fg: 210 20% 98%;
    --editor-highlight: 262 83.3% 57.8%;
    --editor-highlight-fg: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Hide scrollbar but allow scrolling */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Monaco-like syntax highlighting for code editor */
.monaco-syntax {
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  line-height: 1.5;
}

.monaco-line {
  display: flex;
  white-space: pre;
}

.monaco-line-number {
  display: inline-block;
  width: 2.5em;
  text-align: right;
  padding-right: 1em;
  color: #858585;
  user-select: none;
}

/* Light theme syntax colors */
:root {
  --monaco-keyword-color: #0000ff;
  --monaco-string-color: #a31515;
  --monaco-comment-color: #008000;
  --monaco-number-color: #098658;
  --monaco-function-color: #795e26;
  --monaco-tag-color: #800000;
  --monaco-attribute-color: #ff0000;
  --monaco-type-color: #267f99;
  --monaco-variable-color: #001080;
  --monaco-operator-color: #000000;
  --monaco-parameter-color: #001080;
  --monaco-class-color: #267f99;
  --monaco-regex-color: #811f3f;
}

/* Dark theme syntax colors */
.dark {
  --monaco-keyword-color: #569cd6;
  --monaco-string-color: #ce9178;
  --monaco-comment-color: #6a9955;
  --monaco-number-color: #b5cea8;
  --monaco-function-color: #dcdcaa;
  --monaco-tag-color: #569cd6;
  --monaco-attribute-color: #9cdcfe;
  --monaco-type-color: #4ec9b0;
  --monaco-variable-color: #9cdcfe;
  --monaco-operator-color: #d4d4d4;
  --monaco-parameter-color: #9cdcfe;
  --monaco-class-color: #4ec9b0;
  --monaco-regex-color: #d16969;
}

.monaco-keyword {
  color: var(--monaco-keyword-color);
  font-weight: bold;
}

.monaco-string {
  color: var(--monaco-string-color);
}

.monaco-comment {
  color: var(--monaco-comment-color);
  font-style: italic;
}

.monaco-number {
  color: var(--monaco-number-color);
}

.monaco-function {
  color: var(--monaco-function-color);
}

.monaco-tag {
  color: var(--monaco-tag-color);
}

.monaco-attribute {
  color: var(--monaco-attribute-color);
}

.monaco-type {
  color: var(--monaco-type-color);
}

.monaco-variable {
  color: var(--monaco-variable-color);
}

.monaco-operator {
  color: var(--monaco-operator-color);
}

.monaco-parameter {
  color: var(--monaco-parameter-color);
}

.monaco-class {
  color: var(--monaco-class-color);
}

.monaco-regex {
  color: var(--monaco-regex-color);
}

/* Terminal syntax highlighting */
.terminal-prompt {
  color: #75b5aa;
  font-weight: bold;
}

.terminal-command {
  color: #d4d4d4;
}

.terminal-command-name {
  color: #569cd6;
  font-weight: bold;
}

.terminal-url {
  color: #3794ff;
  text-decoration: underline;
}

.terminal-path {
  color: #ce9178;
}

.terminal-success {
  color: #89d185;
}

.terminal-error {
  color: #f14c4c;
}

.terminal-number {
  color: #b5cea8;
}

.terminal-package {
  color: #9cdcfe;
}

.terminal-option {
  color: #dcdcaa;
}

.dark .terminal-prompt {
  color: #75b5aa;
}

.dark .terminal-command {
  color: #d4d4d4;
}

.dark .terminal-command-name {
  color: #569cd6;
}

.dark .terminal-url {
  color: #3794ff;
}

.dark .terminal-path {
  color: #ce9178;
}

.dark .terminal-success {
  color: #89d185;
}

.dark .terminal-error {
  color: #f14c4c;
}

.dark .terminal-number {
  color: #b5cea8;
}

.dark .terminal-package {
  color: #9cdcfe;
}

.dark .terminal-option {
  color: #dcdcaa;
}

/* Fix for clickability issues */
button,
[role="button"],
[tabindex],
a,
input,
select,
textarea,
.cursor-pointer {
  pointer-events: auto !important;
  position: relative;
}

/* Ensure overlays don't block interactions unless intended */
[data-state="open"] {
  pointer-events: auto !important;
}

/* Fix for dialog overlays */
[data-radix-popper-content-wrapper] {
  pointer-events: auto !important;
}

/* Ensure dropdown menus are clickable */
[data-radix-dropdown-menu-content] {
  pointer-events: auto !important;
  z-index: 50;
}

/* Professional File Explorer Styling */
.file-explorer-item {
  @apply transition-all duration-150 ease-in-out;
  @apply hover:bg-accent/30 focus:bg-accent/30;
  @apply border-l-2 border-transparent;
  @apply min-h-[28px] flex items-center;
}

.file-explorer-item:hover {
  @apply border-l-accent/50;
}

.file-explorer-item.selected {
  @apply bg-accent text-accent-foreground font-medium;
  @apply border-l-primary;
}

.file-explorer-icon {
  @apply w-4 h-4 flex-shrink-0 transition-colors duration-150;
}

.file-explorer-text {
  @apply text-sm leading-5 truncate select-none;
  @apply transition-colors duration-150;
}

.file-explorer-header {
  @apply text-base font-semibold text-foreground leading-6;
  @apply tracking-tight;
}

.file-explorer-section-title {
  @apply text-xs font-semibold uppercase tracking-wider;
  @apply text-muted-foreground/80 leading-4;
}

.file-explorer-footer {
  @apply text-xs text-muted-foreground/80 leading-4;
  @apply font-medium;
}

/* Professional button styling */
.file-explorer-button {
  @apply transition-colors duration-150 ease-in-out;
  @apply hover:bg-accent focus:bg-accent focus:outline-none;
  @apply font-medium;
}

/* Professional context menu styling */
.context-menu-item {
  @apply transition-colors duration-150 ease-in-out;
  @apply hover:bg-accent focus:bg-accent focus:outline-none;
  @apply text-sm leading-5 font-medium;
}

.context-menu-separator {
  @apply h-px bg-border mx-2 my-1;
}

/* Professional scrollbar styling */
.file-explorer-scroll {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
}

.file-explorer-scroll::-webkit-scrollbar {
  width: 6px;
}

.file-explorer-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.file-explorer-scroll::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.file-explorer-scroll::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground) / 0.5);
}

/* Search highlighting */
.search-highlight {
  background-color: hsl(var(--primary) / 0.2);
  color: hsl(var(--primary-foreground));
  padding: 0 2px;
  border-radius: 2px;
  font-weight: 600;
}

.dark .search-highlight {
  background-color: hsl(var(--primary) / 0.3);
  color: hsl(var(--primary-foreground));
}

/* Search results styling */
.search-results-container {
  @apply border border-border rounded-md shadow-lg bg-background;
  @apply animate-in fade-in-0 slide-in-from-top-2 duration-200;
}

.search-result-item {
  @apply file-explorer-item;
  @apply hover:bg-accent/50 focus:bg-accent;
}

.search-result-item.selected {
  @apply bg-accent text-accent-foreground;
}

/* Quick search modal */
.quick-search-overlay {
  @apply fixed inset-0 z-50 bg-background/80 backdrop-blur-sm;
  @apply animate-in fade-in-0 duration-200;
}

.quick-search-modal {
  @apply bg-background border border-border rounded-lg shadow-xl;
  @apply animate-in fade-in-0 slide-in-from-top-4 duration-200;
}

/* Drag and Drop Styling */
.dragging {
  @apply opacity-50 transform scale-95;
  @apply transition-all duration-150 ease-in-out;
}

.drag-over {
  @apply bg-accent/20 border-accent;
  @apply transition-colors duration-150;
}

.drop-target {
  @apply relative;
}

.drop-target.drop-before::before {
  content: '';
  @apply absolute top-0 left-0 right-0 h-0.5 bg-primary;
  @apply animate-pulse;
}

.drop-target.drop-after::after {
  content: '';
  @apply absolute bottom-0 left-0 right-0 h-0.5 bg-primary;
  @apply animate-pulse;
}

.drop-target.drop-inside {
  @apply bg-primary/10 border-primary/30 border-2 border-dashed;
  @apply transition-all duration-150;
}

.drag-preview {
  @apply px-3 py-1 bg-background border border-border rounded-md shadow-lg;
  @apply text-sm font-medium text-foreground;
  @apply pointer-events-none select-none;
}

/* File item drag states */
.file-explorer-item.dragging {
  @apply opacity-40 transform scale-95;
}

.file-explorer-item.drag-over {
  @apply bg-accent/30;
}

.file-explorer-item.drop-target {
  @apply ring-2 ring-primary/50 ring-offset-1;
}

/* Folder specific drop styling */
.file-explorer-item[data-item-type="folder"].drop-inside {
  @apply bg-primary/5 border-l-4 border-l-primary;
}

/* Smooth transitions for all drag states */
.file-explorer-item {
  @apply transition-all duration-150 ease-in-out;
}

/* Drag handle cursor */
.file-explorer-item[draggable="true"] {
  cursor: grab;
}

.file-explorer-item[draggable="true"]:active {
  cursor: grabbing;
}

/* File Preview Styling */
.hover-preview-trigger {
  @apply relative;
}

.file-preview-container {
  @apply bg-background border border-border rounded-md shadow-lg;
  @apply animate-in fade-in-0 slide-in-from-top-2 duration-200;
}

.file-preview-header {
  @apply flex items-center gap-2 px-3 py-2 border-b bg-muted/30;
  @apply text-sm font-medium;
}

.file-preview-content {
  @apply overflow-auto;
}

.file-preview-code {
  @apply font-mono text-xs leading-relaxed;
}

.file-preview-line-numbers {
  @apply text-muted-foreground/50 select-none min-w-[2rem] text-right;
}

.file-preview-language-badge {
  @apply absolute top-2 right-2 px-2 py-1 bg-muted/80 rounded text-xs text-muted-foreground;
}

/* Preview Panel Styling */
.preview-panel {
  @apply border-t border-border bg-background;
  @apply transition-all duration-200 ease-in-out;
}

.preview-panel.collapsed {
  @apply h-10;
}

.preview-panel.expanded {
  @apply fixed inset-4 z-50 border border-border rounded-lg shadow-xl;
}

.preview-panel-header {
  @apply flex items-center justify-between px-3 py-2 border-b border-border bg-muted/30;
}

.preview-panel-content {
  @apply overflow-hidden;
}

/* Floating Preview Styling */
.floating-preview {
  @apply fixed z-50 bg-background border border-border rounded-lg shadow-xl;
  @apply animate-in fade-in-0 slide-in-from-top-4 duration-200;
}

.floating-preview-header {
  @apply flex items-center justify-between px-3 py-2 border-b border-border bg-muted/30;
  @apply cursor-move select-none;
}

/* Preview Content Types */
.preview-image {
  @apply flex items-center justify-center p-4;
}

.preview-text {
  @apply p-3 text-xs leading-relaxed;
}

.preview-binary {
  @apply flex items-center justify-center p-8 text-muted-foreground;
}

.preview-error {
  @apply flex items-center justify-center p-4 text-destructive;
}

.preview-loading {
  @apply flex items-center justify-center p-4 text-muted-foreground;
}

/* Syntax highlighting placeholder classes */
.preview-code .keyword {
  @apply text-blue-600 dark:text-blue-400;
}

.preview-code .string {
  @apply text-green-600 dark:text-green-400;
}

.preview-code .comment {
  @apply text-gray-500 dark:text-gray-400 italic;
}

.preview-code .number {
  @apply text-orange-600 dark:text-orange-400;
}

.preview-code .function {
  @apply text-purple-600 dark:text-purple-400;
}
