/**
 * Code File Icon Component
 * Enhanced with comprehensive file type support, theming, and performance optimization
 * Maintains backward compatibility while providing advanced features
 */

import React from 'react'
import { cn } from "@/lib/utils"

export interface CodeFileIconProps {
  extension: string
  className?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}

export const CodeFileIcon = ({
  extension,
  className,
  size = 'md'
}: {
  extension: string
  className?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}) => {
  // Use professional, subtle icon design
  return (
    <ProfessionalFileIcon
      extension={extension}
      size={size}
      className={className}
    />
  )
}

/**
 * Professional File Icon Component
 * Clean, subtle design following modern IDE patterns
 */
const ProfessionalFileIcon = ({
  extension,
  size = 'md',
  className
}: {
  extension: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}) => {
  // Size mappings for professional look
  const sizeClasses = {
    xs: 'w-3 h-3 text-[10px]',
    sm: 'w-4 h-4 text-[11px]',
    md: 'w-4 h-4 text-[11px]', // Moderate size as requested
    lg: 'w-5 h-5 text-xs',
    xl: 'w-6 h-6 text-sm'
  }

  // Professional color scheme - subtle and clean
  const getFileTypeInfo = (ext: string) => {
    const normalizedExt = ext.toLowerCase().replace('.', '')

    const fileTypes: Record<string, { color: string; bg: string; text: string; icon?: React.ReactNode }> = {
      // JavaScript/TypeScript
      js: { color: '#F7DF1E', bg: '#F7DF1E15', text: 'JS' },
      jsx: { color: '#61DAFB', bg: '#61DAFB15', text: 'JSX' },
      ts: { color: '#3178C6', bg: '#3178C615', text: 'TS' },
      tsx: { color: '#3178C6', bg: '#3178C615', text: 'TSX' },

      // Web technologies
      html: { color: '#E34F26', bg: '#E34F2615', text: 'HTML' },
      css: { color: '#1572B6', bg: '#1572B615', text: 'CSS' },
      scss: { color: '#CF649A', bg: '#CF649A15', text: 'SCSS' },
      sass: { color: '#CF649A', bg: '#CF649A15', text: 'SASS' },
      less: { color: '#1D365D', bg: '#1D365D15', text: 'LESS' },

      // Data formats
      json: { color: '#FFA500', bg: '#FFA50015', text: 'JSON' },
      xml: { color: '#FF6600', bg: '#FF660015', text: 'XML' },
      yaml: { color: '#CB171E', bg: '#CB171E15', text: 'YAML' },
      yml: { color: '#CB171E', bg: '#CB171E15', text: 'YML' },
      toml: { color: '#9C4221', bg: '#9C422115', text: 'TOML' },

      // Documentation
      md: { color: '#083FA1', bg: '#083FA115', text: 'MD' },
      txt: { color: '#6B7280', bg: '#6B728015', text: 'TXT' },
      pdf: { color: '#FF0000', bg: '#FF000015', text: 'PDF' },

      // Programming languages
      py: { color: '#3776AB', bg: '#3776AB15', text: 'PY' },
      java: { color: '#ED8B00', bg: '#ED8B0015', text: 'JAVA' },
      c: { color: '#A8B9CC', bg: '#A8B9CC15', text: 'C' },
      cpp: { color: '#00599C', bg: '#00599C15', text: 'C++' },
      cs: { color: '#239120', bg: '#23912015', text: 'C#' },
      php: { color: '#777BB4', bg: '#777BB415', text: 'PHP' },
      rb: { color: '#CC342D', bg: '#CC342D15', text: 'RB' },
      go: { color: '#00ADD8', bg: '#00ADD815', text: 'GO' },
      rs: { color: '#CE422B', bg: '#CE422B15', text: 'RS' },
      swift: { color: '#FA7343', bg: '#FA734315', text: 'SWIFT' },
      kt: { color: '#7F52FF', bg: '#7F52FF15', text: 'KT' },

      // Shell and config
      sh: { color: '#4EAA25', bg: '#4EAA2515', text: 'SH' },
      bash: { color: '#4EAA25', bg: '#4EAA2515', text: 'BASH' },
      zsh: { color: '#4EAA25', bg: '#4EAA2515', text: 'ZSH' },
      fish: { color: '#4EAA25', bg: '#4EAA2515', text: 'FISH' },

      // Images
      png: { color: '#FF6B6B', bg: '#FF6B6B15', text: 'PNG' },
      jpg: { color: '#FF6B6B', bg: '#FF6B6B15', text: 'JPG' },
      jpeg: { color: '#FF6B6B', bg: '#FF6B6B15', text: 'JPEG' },
      gif: { color: '#FF6B6B', bg: '#FF6B6B15', text: 'GIF' },
      svg: { color: '#FFB13B', bg: '#FFB13B15', text: 'SVG' },
      webp: { color: '#FF6B6B', bg: '#FF6B6B15', text: 'WEBP' },

      // Archives
      zip: { color: '#9B59B6', bg: '#9B59B615', text: 'ZIP' },
      rar: { color: '#9B59B6', bg: '#9B59B615', text: 'RAR' },
      tar: { color: '#9B59B6', bg: '#9B59B615', text: 'TAR' },
      gz: { color: '#9B59B6', bg: '#9B59B615', text: 'GZ' },

      // Default
      default: { color: '#6B7280', bg: '#6B728015', text: 'FILE' }
    }

    return fileTypes[normalizedExt] || fileTypes.default
  }

  const fileInfo = getFileTypeInfo(extension)

  return (
    <div
      className={cn(
        'flex items-center justify-center rounded-sm font-medium transition-colors',
        sizeClasses[size],
        className
      )}
      style={{
        color: fileInfo.color,
        backgroundColor: fileInfo.bg
      }}
      title={`${fileInfo.text} file`}
    >
      {fileInfo.text}
    </div>
  )
}

// Legacy implementation for fallback (if needed)
const LegacyCodeFileIcon = ({ extension, className }: { extension: string; className?: string }) => {
  // Define icon colors for different file types
  const getIconColor = () => {
    const colors: Record<string, string> = {
      js: "text-yellow-400",
      ts: "text-blue-500",
      jsx: "text-cyan-400",
      tsx: "text-cyan-500",
      php: "text-indigo-400",
      py: "text-green-500",
      rb: "text-red-500",
      java: "text-amber-600",
      c: "text-blue-400",
      cpp: "text-blue-600",
      cs: "text-purple-500",
      go: "text-cyan-500",
      rs: "text-orange-600",
      html: "text-orange-500",
      css: "text-blue-400",
      scss: "text-pink-500",
      json: "text-yellow-600",
      yaml: "text-purple-400",
      yml: "text-purple-400",
      xml: "text-orange-400",
      md: "text-gray-400",
      sql: "text-blue-300",
      sh: "text-gray-400",
      bash: "text-gray-400",
      graphql: "text-pink-600",
      vue: "text-emerald-500",
      svelte: "text-red-600",
      dart: "text-cyan-600",
      kt: "text-purple-400",
      swift: "text-orange-500",
    }

    return colors[extension] || "text-muted-foreground"
  }

  // Get background color for icon
  const getIconBgColor = () => {
    const bgColors: Record<string, string> = {
      js: "bg-yellow-400/15",
      ts: "bg-blue-500/15",
      jsx: "bg-cyan-400/15",
      tsx: "bg-cyan-500/15",
      php: "bg-indigo-400/15",
      py: "bg-green-500/15",
      rb: "bg-red-500/15",
      java: "bg-amber-600/15",
      c: "bg-blue-400/15",
      cpp: "bg-blue-600/15",
      cs: "bg-purple-500/15",
      go: "bg-cyan-500/15",
      rs: "bg-orange-600/15",
      html: "bg-orange-500/15",
      css: "bg-blue-400/15",
      scss: "bg-pink-500/15",
      json: "bg-yellow-600/15",
      yaml: "bg-purple-400/15",
      yml: "bg-purple-400/15",
      xml: "bg-orange-400/15",
      md: "bg-gray-400/15",
      sql: "bg-blue-300/15",
      sh: "bg-gray-400/15",
      bash: "bg-gray-400/15",
      graphql: "bg-pink-600/15",
      vue: "bg-emerald-500/15",
      svelte: "bg-red-600/15",
      dart: "bg-cyan-600/15",
      kt: "bg-purple-400/15",
      swift: "bg-orange-500/15",
    }

    return bgColors[extension] || "bg-muted-foreground/15"
  }

  // Simple, clear file type indicators
  const getFileIcon = () => {
    switch (extension) {
      case "js":
        return <div className="font-bold">JS</div>
      case "ts":
        return <div className="font-bold">TS</div>
      case "jsx":
        return <div className="font-bold">JSX</div>
      case "tsx":
        return <div className="font-bold">TSX</div>
      case "php":
        return <div className="font-bold">PHP</div>
      case "py":
        return <div className="font-bold">PY</div>
      case "rb":
        return <div className="font-bold">RB</div>
      case "java":
        return <div className="font-bold">JV</div>
      case "c":
        return <div className="font-bold">C</div>
      case "cpp":
        return <div className="font-bold">C++</div>
      case "cs":
        return <div className="font-bold">C#</div>
      case "go":
        return <div className="font-bold">GO</div>
      case "rs":
        return <div className="font-bold">RS</div>
      case "html":
        return <div className="font-bold">{"<>"}</div>
      case "css":
        return <div className="font-bold">CSS</div>
      case "scss":
        return <div className="font-bold">SC</div>
      case "json":
        return <div className="font-bold">{"{}"}</div>
      case "yaml":
      case "yml":
        return <div className="font-bold">YML</div>
      case "xml":
        return <div className="font-bold">XML</div>
      case "md":
        return <div className="font-bold">MD</div>
      case "sql":
        return <div className="font-bold">SQL</div>
      case "sh":
      case "bash":
        return <div className="font-bold">SH</div>
      case "graphql":
        return <div className="font-bold">GQL</div>
      case "vue":
        return <div className="font-bold">VUE</div>
      case "svelte":
        return <div className="font-bold">SV</div>
      case "dart":
        return <div className="font-bold">DRT</div>
      case "kt":
        return <div className="font-bold">KT</div>
      case "swift":
        return <div className="font-bold">SWF</div>
      default:
        return <div className="font-bold">DOC</div>
    }
  }

  return (
    <div
      className={cn(
        "flex items-center justify-center text-xs w-5 h-5 rounded-sm",
        getIconColor(),
        getIconBgColor(),
        className,
      )}
    >
      {getFileIcon()}
    </div>
  )
}

// Export the legacy implementation for potential fallback use
export { LegacyCodeFileIcon }
