/**
 * File Sorting Hook
 * React hook for integrating file sorting with components
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import { FileSystemItem } from '../types'
import { FileSortingManager, SortConfig, SortCriteria, SortDirection } from './FileSortingManager'

export interface FileSortingOptions {
  autoSort?: boolean
  persistConfig?: boolean
  onSortChange?: (config: SortConfig) => void
}

export interface FileSortingResult {
  sortedFiles: FileSystemItem[]
  sortConfig: SortConfig
  setSortCriteria: (criteria: SortCriteria) => void
  setSortDirection: (direction: SortDirection) => void
  toggleSortDirection: () => void
  updateSortConfig: (config: Partial<SortConfig>) => void
  availableCriteria: Array<{ value: SortCriteria; label: string; icon: string }>
  isLoading: boolean
}

export const useFileSorting = (
  files: FileSystemItem[],
  options: FileSortingOptions = {}
): FileSortingResult => {
  const {
    autoSort = true,
    persistConfig = true,
    onSortChange
  } = options

  const [sortConfig, setSortConfig] = useState<SortConfig>({
    criteria: 'name',
    direction: 'asc',
    foldersFirst: true,
    caseSensitive: false
  })
  const [isLoading, setIsLoading] = useState(false)
  const [manager] = useState(() => FileSortingManager.getInstance())

  // Load saved configuration on mount
  useEffect(() => {
    if (persistConfig) {
      manager.loadSortConfig()
      setSortConfig(manager.getSortConfig())
    }
  }, [manager, persistConfig])

  // Sort files when configuration or files change
  const sortedFiles = useMemo(() => {
    if (!autoSort || files.length === 0) return files

    setIsLoading(true)
    
    try {
      // Update manager configuration
      manager.updateSortConfig(sortConfig)
      
      // Sort files
      const sorted = manager.sortFiles(files)
      
      return sorted
    } catch (error) {
      console.error('Error sorting files:', error)
      return files
    } finally {
      setIsLoading(false)
    }
  }, [files, sortConfig, autoSort, manager])

  /**
   * Set sort criteria
   */
  const setSortCriteria = useCallback((criteria: SortCriteria) => {
    const newConfig = { ...sortConfig }
    
    // If same criteria, toggle direction
    if (newConfig.criteria === criteria) {
      newConfig.direction = newConfig.direction === 'asc' ? 'desc' : 'asc'
    } else {
      newConfig.criteria = criteria
      newConfig.direction = 'asc'
    }
    
    setSortConfig(newConfig)
    onSortChange?.(newConfig)
  }, [sortConfig, onSortChange])

  /**
   * Set sort direction
   */
  const setSortDirection = useCallback((direction: SortDirection) => {
    const newConfig = { ...sortConfig, direction }
    setSortConfig(newConfig)
    onSortChange?.(newConfig)
  }, [sortConfig, onSortChange])

  /**
   * Toggle sort direction
   */
  const toggleSortDirection = useCallback(() => {
    const newDirection = sortConfig.direction === 'asc' ? 'desc' : 'asc'
    setSortDirection(newDirection)
  }, [sortConfig.direction, setSortDirection])

  /**
   * Update sort configuration
   */
  const updateSortConfig = useCallback((config: Partial<SortConfig>) => {
    const newConfig = { ...sortConfig, ...config }
    setSortConfig(newConfig)
    onSortChange?.(newConfig)
  }, [sortConfig, onSortChange])

  /**
   * Get available sort criteria
   */
  const availableCriteria = useMemo(() => {
    return manager.getAvailableCriteria()
  }, [manager])

  return {
    sortedFiles,
    sortConfig,
    setSortCriteria,
    setSortDirection,
    toggleSortDirection,
    updateSortConfig,
    availableCriteria,
    isLoading
  }
}

/**
 * Hook for sort indicators and UI helpers
 */
export const useSortIndicators = (sortConfig: SortConfig) => {
  /**
   * Get sort indicator for a criteria
   */
  const getSortIndicator = useCallback((criteria: SortCriteria): string => {
    if (sortConfig.criteria !== criteria) return ''
    return sortConfig.direction === 'asc' ? '↑' : '↓'
  }, [sortConfig])

  /**
   * Check if criteria is currently active
   */
  const isActiveCriteria = useCallback((criteria: SortCriteria): boolean => {
    return sortConfig.criteria === criteria
  }, [sortConfig])

  /**
   * Get CSS classes for sort button
   */
  const getSortButtonClasses = useCallback((criteria: SortCriteria): string => {
    const baseClasses = 'sort-button'
    const activeClasses = isActiveCriteria(criteria) ? 'active' : ''
    return `${baseClasses} ${activeClasses}`.trim()
  }, [isActiveCriteria])

  /**
   * Format sort configuration for display
   */
  const formatSortConfig = useCallback((): string => {
    const criteriaLabels: Record<SortCriteria, string> = {
      name: 'Name',
      type: 'Type',
      size: 'Size',
      dateModified: 'Date Modified',
      dateCreated: 'Date Created'
    }
    
    const label = criteriaLabels[sortConfig.criteria]
    const direction = sortConfig.direction === 'asc' ? 'ascending' : 'descending'
    
    return `${label} (${direction})`
  }, [sortConfig])

  return {
    getSortIndicator,
    isActiveCriteria,
    getSortButtonClasses,
    formatSortConfig
  }
}

/**
 * Hook for file statistics based on sorting
 */
export const useFileSortingStats = (files: FileSystemItem[]) => {
  const stats = useMemo(() => {
    const totalFiles = files.filter(f => f.type !== 'folder').length
    const totalFolders = files.filter(f => f.type === 'folder').length
    
    // Calculate file type distribution
    const typeDistribution: Record<string, number> = {}
    files.forEach(file => {
      if (file.type !== 'folder') {
        const extension = file.name.includes('.') 
          ? file.name.split('.').pop()?.toLowerCase() || 'unknown'
          : 'no-extension'
        typeDistribution[extension] = (typeDistribution[extension] || 0) + 1
      }
    })

    // Get most common file types
    const sortedTypes = Object.entries(typeDistribution)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)

    return {
      totalFiles,
      totalFolders,
      totalItems: totalFiles + totalFolders,
      typeDistribution,
      mostCommonTypes: sortedTypes
    }
  }, [files])

  return stats
}

/**
 * Hook for advanced sorting features
 */
export const useAdvancedSorting = (files: FileSystemItem[]) => {
  const [customSortOrder, setCustomSortOrder] = useState<string[]>([])
  const [pinnedItems, setPinnedItems] = useState<Set<string>>(new Set())

  /**
   * Pin/unpin an item
   */
  const togglePinItem = useCallback((itemId: string) => {
    setPinnedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }, [])

  /**
   * Set custom sort order
   */
  const setCustomOrder = useCallback((order: string[]) => {
    setCustomSortOrder(order)
  }, [])

  /**
   * Apply advanced sorting (pinned items first, then custom order)
   */
  const applyAdvancedSorting = useCallback((sortedFiles: FileSystemItem[]): FileSystemItem[] => {
    const pinned: FileSystemItem[] = []
    const unpinned: FileSystemItem[] = []

    sortedFiles.forEach(file => {
      if (pinnedItems.has(file.id)) {
        pinned.push(file)
      } else {
        unpinned.push(file)
      }
    })

    // Apply custom order if available
    if (customSortOrder.length > 0) {
      unpinned.sort((a, b) => {
        const indexA = customSortOrder.indexOf(a.id)
        const indexB = customSortOrder.indexOf(b.id)
        
        if (indexA === -1 && indexB === -1) return 0
        if (indexA === -1) return 1
        if (indexB === -1) return -1
        
        return indexA - indexB
      })
    }

    return [...pinned, ...unpinned]
  }, [pinnedItems, customSortOrder])

  return {
    pinnedItems,
    customSortOrder,
    togglePinItem,
    setCustomOrder,
    applyAdvancedSorting
  }
}
