/**
 * File Metadata Display Component
 * Shows file information relevant to sorting (size, dates, type)
 */

import React from 'react'
import { Calendar, HardDrive, Tag, Clock } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { cn } from "@/lib/utils"
import { FileSystemItem } from '../types'
import { SortCriteria } from './FileSortingManager'

interface FileMetadataDisplayProps {
  item: FileSystemItem
  showCriteria?: SortCriteria[]
  compact?: boolean
  className?: string
}

export const FileMetadataDisplay: React.FC<FileMetadataDisplayProps> = ({
  item,
  showCriteria = ['size', 'dateModified'],
  compact = false,
  className
}) => {
  const metadata = generateFileMetadata(item)

  if (compact) {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        {showCriteria.includes('size') && item.type !== 'folder' && (
          <Badge variant="outline" className="text-xs h-4 px-1">
            {formatFileSize(metadata.size)}
          </Badge>
        )}
        {showCriteria.includes('dateModified') && (
          <Badge variant="outline" className="text-xs h-4 px-1">
            {formatRelativeDate(metadata.dateModified)}
          </Badge>
        )}
      </div>
    )
  }

  return (
    <div className={cn("space-y-2", className)}>
      {showCriteria.includes('size') && (
        <MetadataItem
          icon={<HardDrive className="w-3 h-3" />}
          label="Size"
          value={item.type === 'folder' ? 'Folder' : formatFileSize(metadata.size)}
        />
      )}
      
      {showCriteria.includes('type') && (
        <MetadataItem
          icon={<Tag className="w-3 h-3" />}
          label="Type"
          value={getFileTypeDescription(item)}
        />
      )}
      
      {showCriteria.includes('dateModified') && (
        <MetadataItem
          icon={<Clock className="w-3 h-3" />}
          label="Modified"
          value={formatFullDate(metadata.dateModified)}
        />
      )}
      
      {showCriteria.includes('dateCreated') && (
        <MetadataItem
          icon={<Calendar className="w-3 h-3" />}
          label="Created"
          value={formatFullDate(metadata.dateCreated)}
        />
      )}
    </div>
  )
}

/**
 * Individual metadata item component
 */
const MetadataItem: React.FC<{
  icon: React.ReactNode
  label: string
  value: string
}> = ({ icon, label, value }) => {
  return (
    <div className="flex items-center gap-2 text-xs">
      <div className="text-muted-foreground">{icon}</div>
      <span className="text-muted-foreground min-w-[4rem]">{label}:</span>
      <span className="font-medium">{value}</span>
    </div>
  )
}

/**
 * File Statistics Component
 */
interface FileStatsProps {
  files: FileSystemItem[]
  className?: string
}

export const FileStats: React.FC<FileStatsProps> = ({ files, className }) => {
  const stats = calculateFileStats(files)

  return (
    <div className={cn("grid grid-cols-2 gap-2 text-xs", className)}>
      <div className="flex items-center gap-1">
        <span className="text-muted-foreground">Files:</span>
        <Badge variant="outline" className="text-xs h-4">
          {stats.fileCount}
        </Badge>
      </div>
      
      <div className="flex items-center gap-1">
        <span className="text-muted-foreground">Folders:</span>
        <Badge variant="outline" className="text-xs h-4">
          {stats.folderCount}
        </Badge>
      </div>
      
      <div className="flex items-center gap-1">
        <span className="text-muted-foreground">Total Size:</span>
        <Badge variant="outline" className="text-xs h-4">
          {formatFileSize(stats.totalSize)}
        </Badge>
      </div>
      
      <div className="flex items-center gap-1">
        <span className="text-muted-foreground">Types:</span>
        <Badge variant="outline" className="text-xs h-4">
          {stats.uniqueTypes}
        </Badge>
      </div>
    </div>
  )
}

/**
 * File Type Distribution Component
 */
interface FileTypeDistributionProps {
  files: FileSystemItem[]
  maxTypes?: number
  className?: string
}

export const FileTypeDistribution: React.FC<FileTypeDistributionProps> = ({
  files,
  maxTypes = 5,
  className
}) => {
  const distribution = calculateTypeDistribution(files)
  const topTypes = distribution.slice(0, maxTypes)

  return (
    <div className={cn("space-y-1", className)}>
      <div className="text-xs font-medium text-muted-foreground mb-2">
        File Types
      </div>
      {topTypes.map(([type, count]) => (
        <div key={type} className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">{getFileTypeIcon(type)}</span>
            <span>{type || 'No extension'}</span>
          </div>
          <Badge variant="outline" className="text-xs h-4">
            {count}
          </Badge>
        </div>
      ))}
    </div>
  )
}

/**
 * Utility functions
 */

function generateFileMetadata(item: FileSystemItem) {
  // Generate realistic metadata based on file characteristics
  const now = new Date()
  const nameHash = hashString(item.name)
  
  // Calculate realistic file size
  const extension = getFileExtension(item.name)
  const baseSize = item.name.length * 100
  const sizeMultipliers: Record<string, number> = {
    'js': 2, 'jsx': 2.5, 'ts': 2.2, 'tsx': 2.7, 'py': 1.8,
    'html': 3, 'css': 2, 'scss': 2.2, 'json': 1.5,
    'png': 100, 'jpg': 80, 'gif': 60, 'svg': 5,
    'mp4': 1000, 'mp3': 300, 'pdf': 50, 'zip': 200
  }
  const multiplier = sizeMultipliers[extension] || 1
  const size = Math.floor(baseSize * multiplier + (nameHash % 1000))
  
  // Calculate realistic dates
  const daysSinceModified = 1 + (nameHash % 60) // 1-60 days ago
  const dateModified = new Date(now.getTime() - (daysSinceModified * 24 * 60 * 60 * 1000))
  const dateCreated = new Date(dateModified.getTime() - ((nameHash % 30) * 24 * 60 * 60 * 1000))
  
  return {
    size: item.type === 'folder' ? 0 : size,
    dateModified,
    dateCreated,
    extension
  }
}

function getFileExtension(filename: string): string {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
}

function hashString(str: string): number {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash
  }
  return Math.abs(hash)
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
}

function formatRelativeDate(date: Date): string {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Yesterday'
  if (diffDays < 7) return `${diffDays}d ago`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}w ago`
  if (diffDays < 365) return `${Math.floor(diffDays / 30)}mo ago`
  return `${Math.floor(diffDays / 365)}y ago`
}

function formatFullDate(date: Date): string {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function getFileTypeDescription(item: FileSystemItem): string {
  if (item.type === 'folder') return 'Folder'
  
  const extension = getFileExtension(item.name)
  if (!extension) return 'File'
  
  const typeDescriptions: Record<string, string> = {
    'js': 'JavaScript File',
    'jsx': 'React Component',
    'ts': 'TypeScript File',
    'tsx': 'TypeScript React Component',
    'html': 'HTML Document',
    'css': 'Stylesheet',
    'scss': 'Sass Stylesheet',
    'json': 'JSON Data',
    'md': 'Markdown Document',
    'txt': 'Text File',
    'png': 'PNG Image',
    'jpg': 'JPEG Image',
    'gif': 'GIF Image',
    'svg': 'SVG Vector',
    'pdf': 'PDF Document',
    'zip': 'Archive'
  }
  
  return typeDescriptions[extension] || `${extension.toUpperCase()} File`
}

function getFileTypeIcon(extension: string): string {
  const icons: Record<string, string> = {
    'js': '🟨', 'jsx': '⚛️', 'ts': '🔷', 'tsx': '⚛️',
    'html': '🌐', 'css': '🎨', 'scss': '🎨', 'json': '📋',
    'md': '📝', 'txt': '📄', 'png': '🖼️', 'jpg': '🖼️',
    'gif': '🖼️', 'svg': '🖼️', 'pdf': '📕', 'zip': '📦'
  }
  
  return icons[extension] || '📄'
}

function calculateFileStats(files: FileSystemItem[]) {
  let fileCount = 0
  let folderCount = 0
  let totalSize = 0
  const types = new Set<string>()
  
  const processFiles = (items: FileSystemItem[]) => {
    items.forEach(item => {
      if (item.type === 'folder') {
        folderCount++
        if (item.files) {
          processFiles(item.files)
        }
      } else {
        fileCount++
        const metadata = generateFileMetadata(item)
        totalSize += metadata.size
        types.add(metadata.extension || 'no-extension')
      }
    })
  }
  
  processFiles(files)
  
  return {
    fileCount,
    folderCount,
    totalSize,
    uniqueTypes: types.size
  }
}

function calculateTypeDistribution(files: FileSystemItem[]): Array<[string, number]> {
  const distribution: Record<string, number> = {}
  
  const processFiles = (items: FileSystemItem[]) => {
    items.forEach(item => {
      if (item.type !== 'folder') {
        const extension = getFileExtension(item.name) || 'no-extension'
        distribution[extension] = (distribution[extension] || 0) + 1
      } else if (item.files) {
        processFiles(item.files)
      }
    })
  }
  
  processFiles(files)
  
  return Object.entries(distribution).sort(([, a], [, b]) => b - a)
}

export default FileMetadataDisplay
