/**
 * Sorting Controls Component
 * UI controls for file sorting with visual indicators
 */

import React, { useState } from 'react'
import { ArrowUpDown, <PERSON>U<PERSON>, ArrowDown, Settings, Pin, PinOff } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu"
import { SortCriteria, SortDirection, SortConfig } from './FileSortingManager'
import { useSortIndicators } from './useFileSorting'

interface SortingControlsProps {
  sortConfig: SortConfig
  onSortChange: (criteria: SortCriteria) => void
  onDirectionChange: (direction: SortDirection) => void
  onConfigChange: (config: Partial<SortConfig>) => void
  availableCriteria: Array<{ value: SortCriteria; label: string; icon: string }>
  className?: string
  compact?: boolean
}

export const SortingControls: React.FC<SortingControlsProps> = ({
  sortConfig,
  onSortChange,
  onDirectionChange,
  onConfigChange,
  availableCriteria,
  className,
  compact = false
}) => {
  const { getSortIndicator, isActiveCriteria, formatSortConfig } = useSortIndicators(sortConfig)

  const currentCriteria = availableCriteria.find(c => c.value === sortConfig.criteria)

  if (compact) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn("h-8 gap-1", className)}
            title={`Sort by ${formatSortConfig()}`}
          >
            <ArrowUpDown className="w-3.5 h-3.5" />
            {currentCriteria && (
              <>
                <span className="text-xs">{currentCriteria.icon}</span>
                <span className="text-xs">
                  {getSortIndicator(sortConfig.criteria)}
                </span>
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <SortingDropdownContent
            sortConfig={sortConfig}
            onSortChange={onSortChange}
            onDirectionChange={onDirectionChange}
            onConfigChange={onConfigChange}
            availableCriteria={availableCriteria}
            getSortIndicator={getSortIndicator}
            isActiveCriteria={isActiveCriteria}
          />
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* Sort Criteria Buttons */}
      <div className="flex items-center gap-1">
        {availableCriteria.map((criteria) => (
          <Button
            key={criteria.value}
            variant={isActiveCriteria(criteria.value) ? "default" : "ghost"}
            size="sm"
            onClick={() => onSortChange(criteria.value)}
            className="h-7 px-2 gap-1"
            title={`Sort by ${criteria.label}`}
          >
            <span className="text-xs">{criteria.icon}</span>
            <span className="text-xs">{criteria.label}</span>
            {isActiveCriteria(criteria.value) && (
              <span className="text-xs">
                {getSortIndicator(criteria.value)}
              </span>
            )}
          </Button>
        ))}
      </div>

      {/* Direction Toggle */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onDirectionChange(sortConfig.direction === 'asc' ? 'desc' : 'asc')}
        className="h-7 w-7 p-0"
        title={`Sort ${sortConfig.direction === 'asc' ? 'descending' : 'ascending'}`}
      >
        {sortConfig.direction === 'asc' ? (
          <ArrowUp className="w-3.5 h-3.5" />
        ) : (
          <ArrowDown className="w-3.5 h-3.5" />
        )}
      </Button>

      {/* Settings */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0"
            title="Sort settings"
          >
            <Settings className="w-3.5 h-3.5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuCheckboxItem
            checked={sortConfig.foldersFirst}
            onCheckedChange={(checked) => onConfigChange({ foldersFirst: checked })}
          >
            Folders First
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={sortConfig.caseSensitive}
            onCheckedChange={(checked) => onConfigChange({ caseSensitive: checked })}
          >
            Case Sensitive
          </DropdownMenuCheckboxItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

/**
 * Dropdown content for compact sorting controls
 */
const SortingDropdownContent: React.FC<{
  sortConfig: SortConfig
  onSortChange: (criteria: SortCriteria) => void
  onDirectionChange: (direction: SortDirection) => void
  onConfigChange: (config: Partial<SortConfig>) => void
  availableCriteria: Array<{ value: SortCriteria; label: string; icon: string }>
  getSortIndicator: (criteria: SortCriteria) => string
  isActiveCriteria: (criteria: SortCriteria) => boolean
}> = ({
  sortConfig,
  onSortChange,
  onDirectionChange,
  onConfigChange,
  availableCriteria,
  getSortIndicator,
  isActiveCriteria
}) => {
  return (
    <>
      <DropdownMenuLabel>Sort By</DropdownMenuLabel>
      {availableCriteria.map((criteria) => (
        <DropdownMenuItem
          key={criteria.value}
          onClick={() => onSortChange(criteria.value)}
          className={cn(
            "flex items-center justify-between",
            isActiveCriteria(criteria.value) && "bg-accent"
          )}
        >
          <div className="flex items-center gap-2">
            <span>{criteria.icon}</span>
            <span>{criteria.label}</span>
          </div>
          {isActiveCriteria(criteria.value) && (
            <span className="text-xs font-mono">
              {getSortIndicator(criteria.value)}
            </span>
          )}
        </DropdownMenuItem>
      ))}
      
      <DropdownMenuSeparator />
      
      <DropdownMenuItem
        onClick={() => onDirectionChange(sortConfig.direction === 'asc' ? 'desc' : 'asc')}
        className="flex items-center gap-2"
      >
        {sortConfig.direction === 'asc' ? (
          <ArrowDown className="w-4 h-4" />
        ) : (
          <ArrowUp className="w-4 h-4" />
        )}
        <span>
          Sort {sortConfig.direction === 'asc' ? 'Descending' : 'Ascending'}
        </span>
      </DropdownMenuItem>
      
      <DropdownMenuSeparator />
      
      <DropdownMenuCheckboxItem
        checked={sortConfig.foldersFirst}
        onCheckedChange={(checked) => onConfigChange({ foldersFirst: checked })}
      >
        Folders First
      </DropdownMenuCheckboxItem>
      <DropdownMenuCheckboxItem
        checked={sortConfig.caseSensitive}
        onCheckedChange={(checked) => onConfigChange({ caseSensitive: checked })}
      >
        Case Sensitive
      </DropdownMenuCheckboxItem>
    </>
  )
}

/**
 * Sort Status Display Component
 */
interface SortStatusProps {
  sortConfig: SortConfig
  totalItems: number
  className?: string
}

export const SortStatus: React.FC<SortStatusProps> = ({
  sortConfig,
  totalItems,
  className
}) => {
  const { formatSortConfig } = useSortIndicators(sortConfig)

  return (
    <div className={cn("flex items-center gap-2 text-xs text-muted-foreground", className)}>
      <Badge variant="outline" className="text-xs">
        {formatSortConfig()}
      </Badge>
      <span>{totalItems} items</span>
      {sortConfig.foldersFirst && (
        <Badge variant="secondary" className="text-xs">
          Folders First
        </Badge>
      )}
    </div>
  )
}

/**
 * Quick Sort Buttons Component
 */
interface QuickSortButtonsProps {
  onSortChange: (criteria: SortCriteria) => void
  sortConfig: SortConfig
  className?: string
}

export const QuickSortButtons: React.FC<QuickSortButtonsProps> = ({
  onSortChange,
  sortConfig,
  className
}) => {
  const { isActiveCriteria } = useSortIndicators(sortConfig)

  const quickSorts: Array<{ criteria: SortCriteria; icon: string; label: string }> = [
    { criteria: 'name', icon: '📝', label: 'Name' },
    { criteria: 'type', icon: '🏷️', label: 'Type' },
    { criteria: 'size', icon: '📏', label: 'Size' },
    { criteria: 'dateModified', icon: '📅', label: 'Modified' }
  ]

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {quickSorts.map((sort) => (
        <Button
          key={sort.criteria}
          variant={isActiveCriteria(sort.criteria) ? "default" : "ghost"}
          size="sm"
          onClick={() => onSortChange(sort.criteria)}
          className="h-6 px-2 gap-1"
          title={`Sort by ${sort.label}`}
        >
          <span className="text-xs">{sort.icon}</span>
          <span className="text-xs">{sort.label}</span>
        </Button>
      ))}
    </div>
  )
}

/**
 * Advanced Sorting Controls Component
 */
interface AdvancedSortingControlsProps {
  pinnedItems: Set<string>
  onTogglePin: (itemId: string) => void
  onResetSort: () => void
  className?: string
}

export const AdvancedSortingControls: React.FC<AdvancedSortingControlsProps> = ({
  pinnedItems,
  onTogglePin,
  onResetSort,
  className
}) => {
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Badge variant="outline" className="text-xs">
        {pinnedItems.size} pinned
      </Badge>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={onResetSort}
        className="h-6 px-2 text-xs"
        title="Reset to default sorting"
      >
        Reset
      </Button>
    </div>
  )
}

export default SortingControls
