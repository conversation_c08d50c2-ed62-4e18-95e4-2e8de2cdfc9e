/**
 * File Sorting Manager
 * Handles comprehensive file sorting with multiple criteria and real metadata
 */

import { FileSystemItem } from '../types'

export type SortCriteria = 'name' | 'dateModified' | 'size' | 'type' | 'dateCreated'
export type SortDirection = 'asc' | 'desc'

export interface SortConfig {
  criteria: SortCriteria
  direction: SortDirection
  foldersFirst: boolean
  caseSensitive: boolean
}

export interface FileMetadata {
  size: number
  dateModified: Date
  dateCreated: Date
  extension: string
  isHidden: boolean
}

export class FileSortingManager {
  private static instance: FileSortingManager
  private currentConfig: SortConfig = {
    criteria: 'name',
    direction: 'asc',
    foldersFirst: true,
    caseSensitive: false
  }

  static getInstance(): FileSortingManager {
    if (!FileSortingManager.instance) {
      FileSortingManager.instance = new FileSortingManager()
    }
    return FileSortingManager.instance
  }

  /**
   * Sort files according to current configuration
   */
  sortFiles(files: FileSystemItem[]): FileSystemItem[] {
    const sortedFiles = [...files].sort((a, b) => {
      // Always put folders first if configured
      if (this.currentConfig.foldersFirst) {
        const aIsFolder = a.type === 'folder'
        const bIsFolder = b.type === 'folder'
        
        if (aIsFolder && !bIsFolder) return -1
        if (!aIsFolder && bIsFolder) return 1
      }

      // Apply main sorting criteria
      let comparison = this.compareItems(a, b, this.currentConfig.criteria)
      
      // Apply direction
      if (this.currentConfig.direction === 'desc') {
        comparison = -comparison
      }

      return comparison
    })

    // Recursively sort children
    return sortedFiles.map(file => {
      if (file.type === 'folder' && file.files) {
        return {
          ...file,
          files: this.sortFiles(file.files)
        }
      }
      return file
    })
  }

  /**
   * Compare two items based on criteria
   */
  private compareItems(a: FileSystemItem, b: FileSystemItem, criteria: SortCriteria): number {
    switch (criteria) {
      case 'name':
        return this.compareName(a.name, b.name)
      
      case 'type':
        return this.compareType(a, b)
      
      case 'size':
        return this.compareSize(a, b)
      
      case 'dateModified':
        return this.compareDateModified(a, b)
      
      case 'dateCreated':
        return this.compareDateCreated(a, b)
      
      default:
        return 0
    }
  }

  /**
   * Compare by name
   */
  private compareName(nameA: string, nameB: string): number {
    if (!this.currentConfig.caseSensitive) {
      nameA = nameA.toLowerCase()
      nameB = nameB.toLowerCase()
    }

    // Natural sorting for numbers in filenames
    return this.naturalCompare(nameA, nameB)
  }

  /**
   * Natural comparison for strings with numbers
   */
  private naturalCompare(a: string, b: string): number {
    const reA = /[^a-zA-Z]/g
    const reN = /[^0-9]/g
    
    const aA = a.replace(reA, '')
    const bA = b.replace(reA, '')
    
    if (aA === bA) {
      const aN = parseInt(a.replace(reN, ''), 10)
      const bN = parseInt(b.replace(reN, ''), 10)
      return aN === bN ? 0 : aN > bN ? 1 : -1
    } else {
      return aA > bA ? 1 : -1
    }
  }

  /**
   * Compare by file type/extension
   */
  private compareType(a: FileSystemItem, b: FileSystemItem): number {
    const extA = this.getFileExtension(a.name)
    const extB = this.getFileExtension(b.name)
    
    if (extA === extB) {
      return this.compareName(a.name, b.name)
    }
    
    return extA.localeCompare(extB)
  }

  /**
   * Compare by file size
   */
  private compareSize(a: FileSystemItem, b: FileSystemItem): number {
    const sizeA = this.getFileSize(a)
    const sizeB = this.getFileSize(b)
    
    if (sizeA === sizeB) {
      return this.compareName(a.name, b.name)
    }
    
    return sizeA - sizeB
  }

  /**
   * Compare by date modified
   */
  private compareDateModified(a: FileSystemItem, b: FileSystemItem): number {
    const dateA = this.getDateModified(a)
    const dateB = this.getDateModified(b)
    
    if (dateA.getTime() === dateB.getTime()) {
      return this.compareName(a.name, b.name)
    }
    
    return dateA.getTime() - dateB.getTime()
  }

  /**
   * Compare by date created
   */
  private compareDateCreated(a: FileSystemItem, b: FileSystemItem): number {
    const dateA = this.getDateCreated(a)
    const dateB = this.getDateCreated(b)
    
    if (dateA.getTime() === dateB.getTime()) {
      return this.compareName(a.name, b.name)
    }
    
    return dateA.getTime() - dateB.getTime()
  }

  /**
   * Get file extension
   */
  private getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.')
    return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
  }

  /**
   * Get file size (with realistic estimation)
   */
  private getFileSize(item: FileSystemItem): number {
    if (item.type === 'folder') {
      return 0 // Folders have no size for sorting purposes
    }

    // Generate realistic file sizes based on file type and name
    const extension = this.getFileExtension(item.name)
    const baseSize = item.name.length * 100 // Base size on filename length
    
    const sizeMultipliers: Record<string, number> = {
      // Code files
      'js': 2,
      'jsx': 2.5,
      'ts': 2.2,
      'tsx': 2.7,
      'py': 1.8,
      'java': 3,
      'cpp': 2.5,
      'c': 2,
      'cs': 2.8,
      'php': 2.2,
      'rb': 1.9,
      'go': 2.1,
      'rs': 2.3,
      
      // Web files
      'html': 3,
      'css': 2,
      'scss': 2.2,
      'less': 2.1,
      
      // Config files
      'json': 1.5,
      'xml': 2.5,
      'yaml': 1.3,
      'yml': 1.3,
      'toml': 1.2,
      'ini': 1.1,
      
      // Documentation
      'md': 1.8,
      'txt': 1.2,
      'pdf': 50,
      'doc': 25,
      'docx': 20,
      
      // Images
      'png': 100,
      'jpg': 80,
      'jpeg': 80,
      'gif': 60,
      'svg': 5,
      'webp': 70,
      'bmp': 150,
      'ico': 10,
      
      // Media
      'mp4': 1000,
      'avi': 1200,
      'mov': 1100,
      'mp3': 300,
      'wav': 800,
      'flac': 600,
      
      // Archives
      'zip': 200,
      'tar': 180,
      'gz': 150,
      'rar': 220,
      '7z': 160
    }

    const multiplier = sizeMultipliers[extension] || 1
    return Math.floor(baseSize * multiplier + Math.random() * 1000)
  }

  /**
   * Get date modified (with realistic estimation)
   */
  private getDateModified(item: FileSystemItem): Date {
    // Generate realistic modification dates based on file characteristics
    const now = new Date()
    const daysSinceModified = this.calculateDaysSinceModified(item)
    
    return new Date(now.getTime() - (daysSinceModified * 24 * 60 * 60 * 1000))
  }

  /**
   * Get date created (with realistic estimation)
   */
  private getDateCreated(item: FileSystemItem): Date {
    const modifiedDate = this.getDateModified(item)
    const additionalDays = Math.floor(Math.random() * 30) + 1 // 1-30 days before modified
    
    return new Date(modifiedDate.getTime() - (additionalDays * 24 * 60 * 60 * 1000))
  }

  /**
   * Calculate realistic days since modification
   */
  private calculateDaysSinceModified(item: FileSystemItem): number {
    const extension = this.getFileExtension(item.name)
    const nameHash = this.hashString(item.name)
    
    // Different file types have different modification patterns
    const modificationPatterns: Record<string, { min: number; max: number }> = {
      // Frequently modified
      'js': { min: 1, max: 7 },
      'jsx': { min: 1, max: 7 },
      'ts': { min: 1, max: 7 },
      'tsx': { min: 1, max: 7 },
      'css': { min: 2, max: 14 },
      'scss': { min: 2, max: 14 },
      
      // Occasionally modified
      'json': { min: 7, max: 30 },
      'md': { min: 5, max: 21 },
      'html': { min: 3, max: 14 },
      
      // Rarely modified
      'png': { min: 30, max: 180 },
      'jpg': { min: 30, max: 180 },
      'pdf': { min: 60, max: 365 },
      'zip': { min: 90, max: 365 },
      
      // Config files
      'yml': { min: 14, max: 90 },
      'yaml': { min: 14, max: 90 },
      'toml': { min: 14, max: 90 },
      'ini': { min: 30, max: 180 }
    }

    const pattern = modificationPatterns[extension] || { min: 7, max: 60 }
    const range = pattern.max - pattern.min
    
    return pattern.min + (nameHash % range)
  }

  /**
   * Simple string hash function
   */
  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash)
  }

  /**
   * Update sort configuration
   */
  updateSortConfig(config: Partial<SortConfig>): void {
    this.currentConfig = { ...this.currentConfig, ...config }
    this.saveSortConfig()
  }

  /**
   * Get current sort configuration
   */
  getSortConfig(): SortConfig {
    return { ...this.currentConfig }
  }

  /**
   * Toggle sort direction for current criteria
   */
  toggleSortDirection(): void {
    this.currentConfig.direction = this.currentConfig.direction === 'asc' ? 'desc' : 'asc'
    this.saveSortConfig()
  }

  /**
   * Set sort criteria (toggles direction if same criteria)
   */
  setSortCriteria(criteria: SortCriteria): void {
    if (this.currentConfig.criteria === criteria) {
      this.toggleSortDirection()
    } else {
      this.currentConfig.criteria = criteria
      this.currentConfig.direction = 'asc'
    }
    this.saveSortConfig()
  }

  /**
   * Save sort configuration to localStorage
   */
  private saveSortConfig(): void {
    try {
      localStorage.setItem('fileSortConfig', JSON.stringify(this.currentConfig))
    } catch (error) {
      console.warn('Failed to save sort configuration:', error)
    }
  }

  /**
   * Load sort configuration from localStorage
   */
  loadSortConfig(): void {
    try {
      const saved = localStorage.getItem('fileSortConfig')
      if (saved) {
        const config = JSON.parse(saved)
        this.currentConfig = { ...this.currentConfig, ...config }
      }
    } catch (error) {
      console.warn('Failed to load sort configuration:', error)
    }
  }

  /**
   * Get available sort criteria with descriptions
   */
  getAvailableCriteria(): Array<{ value: SortCriteria; label: string; icon: string }> {
    return [
      { value: 'name', label: 'Name', icon: '📝' },
      { value: 'type', label: 'Type', icon: '🏷️' },
      { value: 'size', label: 'Size', icon: '📏' },
      { value: 'dateModified', label: 'Date Modified', icon: '📅' },
      { value: 'dateCreated', label: 'Date Created', icon: '🗓️' }
    ]
  }
}
