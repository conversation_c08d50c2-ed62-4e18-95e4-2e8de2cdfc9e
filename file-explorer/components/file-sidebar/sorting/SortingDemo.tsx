/**
 * File Sorting Demo Component
 * Demonstrates file sorting capabilities with interactive examples
 */

import React, { useState } from 'react'
import { ArrowUpDown, BarChart3, Info, Shuffle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from "@/lib/utils"
import { FileSystemItem } from '../types'
import { useFileSorting, useFileSortingStats } from './useFileSorting'
import { SortingControls, QuickSortButtons } from './SortingControls'
import { FileMetadataDisplay, FileStats, FileTypeDistribution } from './FileMetadataDisplay'

// Sample files for demonstration
const sampleFiles: FileSystemItem[] = [
  {
    id: 'folder1',
    name: 'src',
    type: 'folder',
    path: '/project/src',
    files: [
      { id: 'file1', name: 'index.ts', type: 'ts', path: '/project/src/index.ts' },
      { id: 'file2', name: 'App.tsx', type: 'tsx', path: '/project/src/App.tsx' },
      { id: 'file3', name: 'utils.js', type: 'js', path: '/project/src/utils.js' },
      { id: 'file4', name: 'styles.css', type: 'css', path: '/project/src/styles.css' }
    ]
  },
  {
    id: 'folder2',
    name: 'docs',
    type: 'folder',
    path: '/project/docs',
    files: [
      { id: 'file5', name: 'README.md', type: 'md', path: '/project/docs/README.md' },
      { id: 'file6', name: 'API.md', type: 'md', path: '/project/docs/API.md' },
      { id: 'file7', name: 'guide.pdf', type: 'pdf', path: '/project/docs/guide.pdf' }
    ]
  },
  { id: 'file8', name: 'package.json', type: 'json', path: '/project/package.json' },
  { id: 'file9', name: 'tsconfig.json', type: 'json', path: '/project/tsconfig.json' },
  { id: 'file10', name: 'webpack.config.js', type: 'js', path: '/project/webpack.config.js' },
  { id: 'file11', name: 'image.png', type: 'png', path: '/project/image.png' },
  { id: 'file12', name: 'data.zip', type: 'zip', path: '/project/data.zip' }
]

export const SortingDemo: React.FC = () => {
  const [selectedFiles, setSelectedFiles] = useState<FileSystemItem[]>(sampleFiles)
  const [showMetadata, setShowMetadata] = useState(false)

  const fileSorting = useFileSorting(selectedFiles, {
    autoSort: true,
    persistConfig: false,
    onSortChange: (config) => {
      console.log('Sort configuration changed:', config)
    }
  })

  const stats = useFileSortingStats(selectedFiles)

  const shuffleFiles = () => {
    const shuffled = [...selectedFiles].sort(() => Math.random() - 0.5)
    setSelectedFiles(shuffled)
  }

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <ArrowUpDown className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-bold">File Sorting Demo</h2>
        </div>
        <p className="text-muted-foreground">
          Explore different sorting options and see how files are organized
        </p>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowUpDown className="w-4 h-4" />
            Sorting Controls
          </CardTitle>
          <CardDescription>
            Try different sorting criteria and options
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Sorting Controls */}
          <div>
            <h4 className="text-sm font-medium mb-2">Sort Options</h4>
            <SortingControls
              sortConfig={fileSorting.sortConfig}
              onSortChange={fileSorting.setSortCriteria}
              onDirectionChange={fileSorting.setSortDirection}
              onConfigChange={fileSorting.updateSortConfig}
              availableCriteria={fileSorting.availableCriteria}
              compact={false}
            />
          </div>

          {/* Quick Sort Buttons */}
          <div>
            <h4 className="text-sm font-medium mb-2">Quick Sort</h4>
            <QuickSortButtons
              onSortChange={fileSorting.setSortCriteria}
              sortConfig={fileSorting.sortConfig}
            />
          </div>

          {/* Demo Actions */}
          <div className="flex items-center gap-2 pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={shuffleFiles}
              className="gap-1"
            >
              <Shuffle className="w-3 h-3" />
              Shuffle Files
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowMetadata(!showMetadata)}
              className="gap-1"
            >
              <Info className="w-3 h-3" />
              {showMetadata ? 'Hide' : 'Show'} Metadata
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* File Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            File Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="text-sm font-medium mb-2">Overview</h4>
              <FileStats files={selectedFiles} />
            </div>
            
            <div>
              <h4 className="text-sm font-medium mb-2">File Types</h4>
              <FileTypeDistribution files={selectedFiles} maxTypes={5} />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sorted Files Display */}
      <Card>
        <CardHeader>
          <CardTitle>Sorted Files</CardTitle>
          <CardDescription>
            Current sort: {fileSorting.sortConfig.criteria} ({fileSorting.sortConfig.direction})
            {fileSorting.sortConfig.foldersFirst && ' • Folders first'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {fileSorting.sortedFiles.map((item) => (
              <SortedFileItem
                key={item.id}
                item={item}
                showMetadata={showMetadata}
                sortCriteria={fileSorting.sortConfig.criteria}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Sorting Information */}
      <Card>
        <CardHeader>
          <CardTitle>How Sorting Works</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">🔤 Name Sorting</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Uses natural sorting that handles numbers correctly (file1.txt comes before file10.txt)
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Case-insensitive by default</li>
                <li>• Numbers sorted numerically</li>
                <li>• Special characters handled properly</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">📏 Size Sorting</h4>
              <p className="text-sm text-muted-foreground mb-2">
                File sizes calculated based on realistic estimates for each file type
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Code files: 1-5KB typically</li>
                <li>• Images: 50-200KB typically</li>
                <li>• Media files: 500KB-2MB typically</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">📅 Date Sorting</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Modification dates based on realistic patterns for different file types
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Code files: Recently modified</li>
                <li>• Config files: Occasionally modified</li>
                <li>• Media files: Rarely modified</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">🏷️ Type Sorting</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Groups files by extension, then sorts by name within each group
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Extensions sorted alphabetically</li>
                <li>• Files without extensions grouped</li>
                <li>• Folders always first (if enabled)</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Individual sorted file item component
 */
const SortedFileItem: React.FC<{
  item: FileSystemItem
  showMetadata: boolean
  sortCriteria: string
}> = ({ item, showMetadata, sortCriteria }) => {
  const isFolder = item.type === 'folder'

  return (
    <div className="flex items-center justify-between p-3 border border-border rounded-md hover:bg-accent/20 transition-colors">
      <div className="flex items-center gap-3 min-w-0 flex-1">
        <span className="text-lg flex-shrink-0">
          {isFolder ? '📁' : getFileIcon(item.name)}
        </span>
        
        <div className="min-w-0 flex-1">
          <div className="font-medium truncate">{item.name}</div>
          {isFolder && item.files && (
            <div className="text-xs text-muted-foreground">
              {item.files.length} item{item.files.length !== 1 ? 's' : ''}
            </div>
          )}
        </div>
      </div>

      {showMetadata && !isFolder && (
        <div className="flex-shrink-0">
          <FileMetadataDisplay
            item={item}
            showCriteria={[sortCriteria as any]}
            compact={true}
          />
        </div>
      )}
    </div>
  )
}

/**
 * Get file icon based on extension
 */
function getFileIcon(filename: string): string {
  const extension = filename.split('.').pop()?.toLowerCase() || ''
  
  const icons: Record<string, string> = {
    'js': '🟨', 'jsx': '⚛️', 'ts': '🔷', 'tsx': '⚛️',
    'html': '🌐', 'css': '🎨', 'scss': '🎨', 'json': '📋',
    'md': '📝', 'txt': '📄', 'png': '🖼️', 'jpg': '🖼️',
    'pdf': '📕', 'zip': '📦', 'mp4': '🎬', 'mp3': '🎵'
  }
  
  return icons[extension] || '📄'
}

export default SortingDemo
