/**
 * Hover Preview Component
 * Shows file preview on hover with intelligent positioning
 */

import React, { useState, useEffect, useRef } from 'react'
import { createPortal } from 'react-dom'
import { FileSystemItem } from '../types'
import { FilePreview } from './FilePreview'
import { cn } from "@/lib/utils"

interface HoverPreviewProps {
  item: FileSystemItem
  children: React.ReactNode
  delay?: number
  disabled?: boolean
  maxWidth?: number
  maxHeight?: number
}

export const HoverPreview: React.FC<HoverPreviewProps> = ({
  item,
  children,
  delay = 500,
  disabled = false,
  maxWidth = 400,
  maxHeight = 300
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [shouldShow, setShouldShow] = useState(false)
  const hoverTimeoutRef = useRef<NodeJS.Timeout>()
  const hideTimeoutRef = useRef<NodeJS.Timeout>()
  const triggerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current)
      if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current)
    }
  }, [])

  const handleMouseEnter = (event: React.MouseEvent) => {
    if (disabled || item.type === 'folder') return

    // Clear any existing timeouts
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
      hideTimeoutRef.current = undefined
    }

    // Calculate position
    const rect = event.currentTarget.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let x = rect.right + 10
    let y = rect.top

    // Adjust if preview would go off-screen horizontally
    if (x + maxWidth > viewportWidth) {
      x = rect.left - maxWidth - 10
    }

    // Adjust if preview would go off-screen vertically
    if (y + maxHeight > viewportHeight) {
      y = viewportHeight - maxHeight - 10
    }

    // Ensure minimum margins
    x = Math.max(10, x)
    y = Math.max(10, y)

    setPosition({ x, y })

    // Set timeout to show preview
    hoverTimeoutRef.current = setTimeout(() => {
      setShouldShow(true)
      setIsVisible(true)
    }, delay)
  }

  const handleMouseLeave = () => {
    // Clear show timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
      hoverTimeoutRef.current = undefined
    }

    // Set timeout to hide preview (allows moving to preview)
    hideTimeoutRef.current = setTimeout(() => {
      setIsVisible(false)
      setShouldShow(false)
    }, 100)
  }

  const handlePreviewMouseEnter = () => {
    // Cancel hide timeout when hovering over preview
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
      hideTimeoutRef.current = undefined
    }
  }

  const handlePreviewMouseLeave = () => {
    // Hide immediately when leaving preview
    setIsVisible(false)
    setShouldShow(false)
  }

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="hover-preview-trigger"
      >
        {children}
      </div>

      {shouldShow && createPortal(
        <div
          className={cn(
            "fixed z-50 pointer-events-auto transition-opacity duration-200",
            isVisible ? "opacity-100" : "opacity-0"
          )}
          style={{
            left: position.x,
            top: position.y,
            maxWidth,
            maxHeight
          }}
          onMouseEnter={handlePreviewMouseEnter}
          onMouseLeave={handlePreviewMouseLeave}
        >
          <div className="bg-background border border-border rounded-lg shadow-xl overflow-hidden">
            <FilePreview
              item={item}
              maxHeight={maxHeight - 20}
              showHeader={true}
              onError={(error) => {
                console.warn('Preview error:', error)
                setIsVisible(false)
                setShouldShow(false)
              }}
            />
          </div>
        </div>,
        document.body
      )}
    </>
  )
}

/**
 * Quick Preview Panel Component
 * Shows preview in a fixed panel (for selection-based preview)
 */
interface QuickPreviewPanelProps {
  item: FileSystemItem | null
  className?: string
  onClose?: () => void
}

export const QuickPreviewPanel: React.FC<QuickPreviewPanelProps> = ({
  item,
  className,
  onClose
}) => {
  if (!item) {
    return (
      <div className={cn("flex items-center justify-center p-8 text-muted-foreground", className)}>
        <div className="text-center">
          <div className="text-sm">No file selected</div>
          <div className="text-xs mt-1">Select a file to see preview</div>
        </div>
      </div>
    )
  }

  if (item.type === 'folder') {
    return (
      <div className={cn("flex items-center justify-center p-8 text-muted-foreground", className)}>
        <div className="text-center">
          <div className="text-sm">Folder selected</div>
          <div className="text-xs mt-1">{item.name}</div>
          {item.files && (
            <div className="text-xs mt-1">
              {item.files.length} item{item.files.length !== 1 ? 's' : ''}
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <FilePreview
        item={item}
        showHeader={true}
        maxHeight={400}
      />
    </div>
  )
}

/**
 * Preview Settings Hook
 */
export const usePreviewSettings = () => {
  const [settings, setSettings] = useState({
    enableHoverPreview: true,
    hoverDelay: 500,
    maxPreviewSize: 1024 * 1024, // 1MB
    showLineNumbers: true,
    enableSyntaxHighlighting: true
  })

  const updateSettings = (newSettings: Partial<typeof settings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }))
    // Save to localStorage
    localStorage.setItem('filePreviewSettings', JSON.stringify({ ...settings, ...newSettings }))
  }

  // Load settings from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('filePreviewSettings')
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setSettings(prev => ({ ...prev, ...parsed }))
      } catch (error) {
        console.warn('Failed to load preview settings:', error)
      }
    }
  }, [])

  return { settings, updateSettings }
}

export default HoverPreview
