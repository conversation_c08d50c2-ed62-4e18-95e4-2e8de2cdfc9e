/**
 * File Preview Manager
 * Handles file content loading and preview generation for different file types
 */

import { FileSystemItem } from '../types'

export interface PreviewContent {
  type: 'text' | 'image' | 'code' | 'binary' | 'error'
  content: string
  language?: string
  size?: number
  dimensions?: { width: number; height: number }
  error?: string
}

export interface PreviewOptions {
  maxSize?: number // Maximum file size to preview (in bytes)
  maxLines?: number // Maximum lines for text files
  timeout?: number // Timeout for loading content
}

export class FilePreviewManager {
  private static instance: FilePreviewManager
  private cache = new Map<string, PreviewContent>()
  private loadingPromises = new Map<string, Promise<PreviewContent>>()

  static getInstance(): FilePreviewManager {
    if (!FilePreviewManager.instance) {
      FilePreviewManager.instance = new FilePreviewManager()
    }
    return FilePreviewManager.instance
  }

  /**
   * Get preview content for a file
   */
  async getPreview(item: FileSystemItem, options: PreviewOptions = {}): Promise<PreviewContent> {
    const {
      maxSize = 1024 * 1024, // 1MB default
      maxLines = 100,
      timeout = 5000
    } = options

    const cacheKey = this.getCacheKey(item, options)
    
    // Return cached content if available
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!
    }

    // Return existing loading promise if in progress
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey)!
    }

    // Start loading content
    const loadingPromise = this.loadPreviewContent(item, options)
    this.loadingPromises.set(cacheKey, loadingPromise)

    try {
      const content = await Promise.race([
        loadingPromise,
        this.createTimeoutPromise(timeout)
      ])

      this.cache.set(cacheKey, content)
      return content
    } catch (error) {
      const errorContent: PreviewContent = {
        type: 'error',
        content: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
      this.cache.set(cacheKey, errorContent)
      return errorContent
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }

  /**
   * Load preview content based on file type
   */
  private async loadPreviewContent(item: FileSystemItem, options: PreviewOptions): Promise<PreviewContent> {
    const fileType = this.getFileType(item)
    
    switch (fileType) {
      case 'image':
        return this.loadImagePreview(item)
      case 'text':
      case 'code':
        return this.loadTextPreview(item, options)
      case 'binary':
        return this.loadBinaryPreview(item)
      default:
        return {
          type: 'error',
          content: '',
          error: 'Unsupported file type'
        }
    }
  }

  /**
   * Load image preview
   */
  private async loadImagePreview(item: FileSystemItem): Promise<PreviewContent> {
    try {
      // For now, return a placeholder since we don't have actual file access
      return {
        type: 'image',
        content: item.path || '',
        dimensions: { width: 200, height: 150 }
      }
    } catch (error) {
      return {
        type: 'error',
        content: '',
        error: 'Failed to load image'
      }
    }
  }

  /**
   * Load text/code preview
   */
  private async loadTextPreview(item: FileSystemItem, options: PreviewOptions): Promise<PreviewContent> {
    try {
      // For now, return sample content since we don't have actual file access
      const language = this.getLanguageFromExtension(item.name)
      const sampleContent = this.generateSampleContent(item, language)
      
      return {
        type: this.isCodeFile(item) ? 'code' : 'text',
        content: sampleContent,
        language,
        size: sampleContent.length
      }
    } catch (error) {
      return {
        type: 'error',
        content: '',
        error: 'Failed to load text content'
      }
    }
  }

  /**
   * Load binary file preview
   */
  private async loadBinaryPreview(item: FileSystemItem): Promise<PreviewContent> {
    return {
      type: 'binary',
      content: `Binary file: ${item.name}`,
      size: 0 // Would be actual file size
    }
  }

  /**
   * Determine file type for preview
   */
  private getFileType(item: FileSystemItem): 'image' | 'text' | 'code' | 'binary' {
    const extension = this.getExtension(item.name).toLowerCase()
    
    const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'bmp', 'ico']
    const textExtensions = ['txt', 'md', 'json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg']
    const codeExtensions = [
      'js', 'jsx', 'ts', 'tsx', 'html', 'css', 'scss', 'sass', 'less',
      'py', 'java', 'c', 'cpp', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt',
      'sh', 'bash', 'zsh', 'fish', 'sql', 'r', 'scala', 'clj', 'hs', 'elm'
    ]

    if (imageExtensions.includes(extension)) return 'image'
    if (codeExtensions.includes(extension)) return 'code'
    if (textExtensions.includes(extension)) return 'text'
    return 'binary'
  }

  /**
   * Check if file is a code file
   */
  private isCodeFile(item: FileSystemItem): boolean {
    return this.getFileType(item) === 'code'
  }

  /**
   * Get programming language from file extension
   */
  private getLanguageFromExtension(filename: string): string {
    const extension = this.getExtension(filename).toLowerCase()
    
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'py': 'python',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'swift': 'swift',
      'kt': 'kotlin',
      'sh': 'bash',
      'bash': 'bash',
      'zsh': 'bash',
      'fish': 'bash',
      'sql': 'sql',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown'
    }

    return languageMap[extension] || 'text'
  }

  /**
   * Generate sample content for demonstration
   */
  private generateSampleContent(item: FileSystemItem, language: string): string {
    const samples: Record<string, string> = {
      'javascript': `// ${item.name}\nimport React from 'react'\n\nconst Component = () => {\n  return <div>Hello World</div>\n}\n\nexport default Component`,
      'typescript': `// ${item.name}\ninterface Props {\n  name: string\n  age: number\n}\n\nconst User: React.FC<Props> = ({ name, age }) => {\n  return <div>{name} is {age} years old</div>\n}`,
      'python': `# ${item.name}\ndef hello_world():\n    print("Hello, World!")\n    return True\n\nif __name__ == "__main__":\n    hello_world()`,
      'css': `/* ${item.name} */\n.container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n}\n\n.button {\n  padding: 10px 20px;\n  border-radius: 4px;\n}`,
      'json': `{\n  "name": "${item.name}",\n  "version": "1.0.0",\n  "description": "Sample JSON file",\n  "main": "index.js",\n  "scripts": {\n    "start": "node index.js"\n  }\n}`,
      'markdown': `# ${item.name}\n\nThis is a sample markdown file.\n\n## Features\n\n- Feature 1\n- Feature 2\n- Feature 3\n\n\`\`\`javascript\nconsole.log('Hello World')\n\`\`\``
    }

    return samples[language] || `// ${item.name}\n// This is a sample ${language} file\n// Content would be loaded from the actual file`
  }

  /**
   * Helper methods
   */
  private getExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.')
    return lastDot > 0 ? filename.substring(lastDot + 1) : ''
  }

  private getCacheKey(item: FileSystemItem, options: PreviewOptions): string {
    return `${item.id}-${item.path}-${JSON.stringify(options)}`
  }

  private createTimeoutPromise(timeout: number): Promise<PreviewContent> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Preview timeout')), timeout)
    })
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
    this.loadingPromises.clear()
  }

  /**
   * Get cache size
   */
  getCacheSize(): number {
    return this.cache.size
  }
}
