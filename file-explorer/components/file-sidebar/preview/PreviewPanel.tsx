/**
 * Preview Panel Component
 * Dedicated panel for showing file previews in the sidebar
 */

import React, { useState } from 'react'
import { X, Eye, EyeOff, Settings, Maximize2, Minimize2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from "@/lib/utils"
import { FileSystemItem } from '../types'
import { QuickPreviewPanel, usePreviewSettings } from './HoverPreview'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem
} from "@/components/ui/dropdown-menu"

interface PreviewPanelProps {
  selectedFile: FileSystemItem | null
  onClose?: () => void
  className?: string
  collapsible?: boolean
}

export const PreviewPanel: React.FC<PreviewPanelProps> = ({
  selectedFile,
  onClose,
  className,
  collapsible = true
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const { settings, updateSettings } = usePreviewSettings()

  const toggleCollapsed = () => {
    setIsCollapsed(!isCollapsed)
    if (isExpanded) setIsExpanded(false)
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
    if (isCollapsed) setIsCollapsed(false)
  }

  return (
    <div className={cn(
      "border-t border-border bg-background transition-all duration-200",
      isCollapsed && "h-10",
      isExpanded && "fixed inset-4 z-50 border border-border rounded-lg shadow-xl",
      !isCollapsed && !isExpanded && "h-80",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between px-3 py-2 border-b border-border bg-muted/30">
        <div className="flex items-center gap-2">
          <Eye className="w-4 h-4 text-muted-foreground" />
          <span className="text-sm font-medium">Preview</span>
          {selectedFile && !isCollapsed && (
            <span className="text-xs text-muted-foreground truncate max-w-32">
              {selectedFile.name}
            </span>
          )}
        </div>

        <div className="flex items-center gap-1">
          {/* Settings Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <Settings className="w-3 h-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuCheckboxItem
                checked={settings.enableHoverPreview}
                onCheckedChange={(checked) => updateSettings({ enableHoverPreview: checked })}
              >
                Enable Hover Preview
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={settings.showLineNumbers}
                onCheckedChange={(checked) => updateSettings({ showLineNumbers: checked })}
              >
                Show Line Numbers
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={settings.enableSyntaxHighlighting}
                onCheckedChange={(checked) => updateSettings({ enableSyntaxHighlighting: checked })}
              >
                Syntax Highlighting
              </DropdownMenuCheckboxItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => updateSettings({ hoverDelay: 300 })}>
                Fast Hover (300ms)
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => updateSettings({ hoverDelay: 500 })}>
                Normal Hover (500ms)
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => updateSettings({ hoverDelay: 800 })}>
                Slow Hover (800ms)
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Expand/Collapse Controls */}
          {collapsible && (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={toggleExpanded}
                title={isExpanded ? "Minimize" : "Maximize"}
              >
                {isExpanded ? (
                  <Minimize2 className="w-3 h-3" />
                ) : (
                  <Maximize2 className="w-3 h-3" />
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={toggleCollapsed}
                title={isCollapsed ? "Expand" : "Collapse"}
              >
                {isCollapsed ? (
                  <Eye className="w-3 h-3" />
                ) : (
                  <EyeOff className="w-3 h-3" />
                )}
              </Button>
            </>
          )}

          {/* Close Button */}
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={onClose}
              title="Close Preview"
            >
              <X className="w-3 h-3" />
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      {!isCollapsed && (
        <div className={cn(
          "overflow-hidden",
          isExpanded ? "h-[calc(100%-2.5rem)]" : "h-[calc(100%-2.5rem)]"
        )}>
          <QuickPreviewPanel
            item={selectedFile}
            className="h-full"
          />
        </div>
      )}

      {/* Expanded Mode Backdrop */}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-background/80 backdrop-blur-sm z-40"
          onClick={() => setIsExpanded(false)}
        />
      )}
    </div>
  )
}

/**
 * Preview Toggle Button Component
 * Button to show/hide the preview panel
 */
interface PreviewToggleProps {
  isVisible: boolean
  onToggle: () => void
  className?: string
}

export const PreviewToggle: React.FC<PreviewToggleProps> = ({
  isVisible,
  onToggle,
  className
}) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onToggle}
      className={cn("h-8 w-8 p-0", className)}
      title={isVisible ? "Hide Preview" : "Show Preview"}
    >
      {isVisible ? (
        <EyeOff className="w-4 h-4" />
      ) : (
        <Eye className="w-4 h-4" />
      )}
    </Button>
  )
}

/**
 * Floating Preview Window Component
 * Detachable preview window
 */
interface FloatingPreviewProps {
  selectedFile: FileSystemItem | null
  onClose: () => void
  position?: { x: number; y: number }
}

export const FloatingPreview: React.FC<FloatingPreviewProps> = ({
  selectedFile,
  onClose,
  position = { x: 100, y: 100 }
}) => {
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [windowPosition, setWindowPosition] = useState(position)

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragOffset({
      x: e.clientX - windowPosition.x,
      y: e.clientY - windowPosition.y
    })
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setWindowPosition({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, dragOffset])

  return (
    <div
      className="fixed z-50 w-96 h-80 bg-background border border-border rounded-lg shadow-xl overflow-hidden"
      style={{
        left: windowPosition.x,
        top: windowPosition.y
      }}
    >
      {/* Draggable Header */}
      <div
        className="flex items-center justify-between px-3 py-2 border-b border-border bg-muted/30 cursor-move"
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center gap-2">
          <Eye className="w-4 h-4 text-muted-foreground" />
          <span className="text-sm font-medium">Preview</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={onClose}
        >
          <X className="w-3 h-3" />
        </Button>
      </div>

      {/* Content */}
      <div className="h-[calc(100%-2.5rem)]">
        <QuickPreviewPanel
          item={selectedFile}
          className="h-full"
        />
      </div>
    </div>
  )
}

export default PreviewPanel
