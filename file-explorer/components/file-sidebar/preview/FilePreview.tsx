/**
 * File Preview Component
 * Displays file previews with syntax highlighting and image support
 */

import React, { useState, useEffect } from 'react'
import { FileText, Image, Code, AlertCircle, Loader2, FileX } from 'lucide-react'
import { cn } from "@/lib/utils"
import { FileSystemItem } from '../types'
import { FilePreviewManager, PreviewContent } from './FilePreviewManager'

interface FilePreviewProps {
  item: FileSystemItem
  className?: string
  maxHeight?: number
  showHeader?: boolean
  onError?: (error: string) => void
}

export const FilePreview: React.FC<FilePreviewProps> = ({
  item,
  className,
  maxHeight = 300,
  showHeader = true,
  onError
}) => {
  const [content, setContent] = useState<PreviewContent | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const previewManager = FilePreviewManager.getInstance()

  useEffect(() => {
    loadPreview()
  }, [item.id, item.path])

  const loadPreview = async () => {
    setLoading(true)
    setError(null)

    try {
      const previewContent = await previewManager.getPreview(item, {
        maxSize: 1024 * 1024, // 1MB
        maxLines: 50,
        timeout: 3000
      })

      if (previewContent.type === 'error') {
        setError(previewContent.error || 'Failed to load preview')
        onError?.(previewContent.error || 'Failed to load preview')
      } else {
        setContent(previewContent)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const getPreviewIcon = () => {
    if (!content) return FileText
    
    switch (content.type) {
      case 'image': return Image
      case 'code': return Code
      case 'text': return FileText
      case 'error': return AlertCircle
      default: return FileX
    }
  }

  const PreviewIcon = getPreviewIcon()

  if (loading) {
    return (
      <div className={cn("flex items-center justify-center p-4 border rounded-md bg-muted/20", className)}>
        <Loader2 className="w-4 h-4 animate-spin mr-2" />
        <span className="text-sm text-muted-foreground">Loading preview...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn("flex items-center justify-center p-4 border rounded-md bg-destructive/10", className)}>
        <AlertCircle className="w-4 h-4 text-destructive mr-2" />
        <span className="text-sm text-destructive">{error}</span>
      </div>
    )
  }

  if (!content) {
    return (
      <div className={cn("flex items-center justify-center p-4 border rounded-md bg-muted/20", className)}>
        <FileX className="w-4 h-4 text-muted-foreground mr-2" />
        <span className="text-sm text-muted-foreground">No preview available</span>
      </div>
    )
  }

  return (
    <div className={cn("border rounded-md bg-background overflow-hidden", className)}>
      {showHeader && (
        <div className="flex items-center gap-2 px-3 py-2 border-b bg-muted/30">
          <PreviewIcon className="w-4 h-4 text-muted-foreground" />
          <span className="text-sm font-medium truncate">{item.name}</span>
          {content.size && (
            <span className="text-xs text-muted-foreground ml-auto">
              {formatFileSize(content.size)}
            </span>
          )}
        </div>
      )}

      <div 
        className="overflow-auto"
        style={{ maxHeight: showHeader ? maxHeight - 40 : maxHeight }}
      >
        {content.type === 'image' && (
          <ImagePreview content={content} />
        )}
        
        {(content.type === 'text' || content.type === 'code') && (
          <TextPreview content={content} />
        )}
        
        {content.type === 'binary' && (
          <BinaryPreview content={content} />
        )}
      </div>
    </div>
  )
}

/**
 * Image Preview Component
 */
const ImagePreview: React.FC<{ content: PreviewContent }> = ({ content }) => {
  return (
    <div className="flex items-center justify-center p-4">
      <div className="text-center">
        <Image className="w-16 h-16 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">Image Preview</p>
        <p className="text-xs text-muted-foreground/70">{content.content}</p>
        {content.dimensions && (
          <p className="text-xs text-muted-foreground/70 mt-1">
            {content.dimensions.width} × {content.dimensions.height}
          </p>
        )}
      </div>
    </div>
  )
}

/**
 * Text/Code Preview Component
 */
const TextPreview: React.FC<{ content: PreviewContent }> = ({ content }) => {
  const lines = content.content.split('\n')
  
  return (
    <div className="relative">
      <pre className="text-xs leading-relaxed p-3 overflow-x-auto">
        <code className={cn(
          "block",
          content.type === 'code' && "font-mono"
        )}>
          {lines.map((line, index) => (
            <div key={index} className="flex">
              <span className="text-muted-foreground/50 mr-3 select-none min-w-[2rem] text-right">
                {index + 1}
              </span>
              <span className="flex-1">{line || ' '}</span>
            </div>
          ))}
        </code>
      </pre>
      
      {content.language && (
        <div className="absolute top-2 right-2 px-2 py-1 bg-muted/80 rounded text-xs text-muted-foreground">
          {content.language}
        </div>
      )}
    </div>
  )
}

/**
 * Binary File Preview Component
 */
const BinaryPreview: React.FC<{ content: PreviewContent }> = ({ content }) => {
  return (
    <div className="flex items-center justify-center p-8">
      <div className="text-center">
        <FileX className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">{content.content}</p>
        <p className="text-xs text-muted-foreground/70 mt-1">Binary file - no preview available</p>
      </div>
    </div>
  )
}

/**
 * Format file size for display
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
}

export default FilePreview
