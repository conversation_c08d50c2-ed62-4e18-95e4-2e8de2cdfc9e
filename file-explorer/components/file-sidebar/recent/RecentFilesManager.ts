/**
 * Recent Files Manager
 * Handles tracking and management of recently accessed files with real persistence
 */

import { FileSystemItem } from '../types'

export interface RecentFileEntry {
  id: string
  file: FileSystemItem
  lastAccessed: Date
  accessCount: number
  projectPath?: string
  operation: 'opened' | 'modified' | 'created' | 'viewed'
}

export interface RecentFilesConfig {
  maxRecentFiles: number
  maxAge: number // in days
  trackOperations: ('opened' | 'modified' | 'created' | 'viewed')[]
  groupByProject: boolean
  autoCleanup: boolean
}

export class RecentFilesManager {
  private static instance: RecentFilesManager
  private recentFiles: Map<string, RecentFileEntry> = new Map()
  private config: RecentFilesConfig = {
    maxRecentFiles: 50,
    maxAge: 30, // 30 days
    trackOperations: ['opened', 'modified', 'created', 'viewed'],
    groupByProject: true,
    autoCleanup: true
  }

  static getInstance(): RecentFilesManager {
    if (!RecentFilesManager.instance) {
      RecentFilesManager.instance = new RecentFilesManager()
    }
    return RecentFilesManager.instance
  }

  constructor() {
    this.loadRecentFiles()
    this.setupAutoCleanup()
  }

  /**
   * Add or update a recent file entry
   */
  addRecentFile(file: FileSystemItem, operation: 'opened' | 'modified' | 'created' | 'viewed', projectPath?: string): void {
    if (!this.config.trackOperations.includes(operation)) {
      return
    }

    const key = this.getFileKey(file)
    const now = new Date()
    
    const existingEntry = this.recentFiles.get(key)
    
    if (existingEntry) {
      // Update existing entry
      existingEntry.lastAccessed = now
      existingEntry.accessCount++
      existingEntry.operation = operation
      if (projectPath) {
        existingEntry.projectPath = projectPath
      }
    } else {
      // Create new entry
      const newEntry: RecentFileEntry = {
        id: this.generateEntryId(),
        file: { ...file },
        lastAccessed: now,
        accessCount: 1,
        projectPath,
        operation
      }
      this.recentFiles.set(key, newEntry)
    }

    this.enforceMaxFiles()
    this.saveRecentFiles()
  }

  /**
   * Get recent files sorted by last accessed
   */
  getRecentFiles(limit?: number): RecentFileEntry[] {
    const entries = Array.from(this.recentFiles.values())
    
    // Filter out expired entries
    const validEntries = entries.filter(entry => {
      const daysSinceAccess = this.getDaysSince(entry.lastAccessed)
      return daysSinceAccess <= this.config.maxAge
    })

    // Sort by last accessed (most recent first)
    validEntries.sort((a, b) => b.lastAccessed.getTime() - a.lastAccessed.getTime())

    return limit ? validEntries.slice(0, limit) : validEntries
  }

  /**
   * Get recent files grouped by project
   */
  getRecentFilesByProject(): Record<string, RecentFileEntry[]> {
    const recentFiles = this.getRecentFiles()
    const grouped: Record<string, RecentFileEntry[]> = {}

    recentFiles.forEach(entry => {
      const projectKey = entry.projectPath || 'No Project'
      if (!grouped[projectKey]) {
        grouped[projectKey] = []
      }
      grouped[projectKey].push(entry)
    })

    return grouped
  }

  /**
   * Get recent files by operation type
   */
  getRecentFilesByOperation(operation: 'opened' | 'modified' | 'created' | 'viewed'): RecentFileEntry[] {
    return this.getRecentFiles().filter(entry => entry.operation === operation)
  }

  /**
   * Get most frequently accessed files
   */
  getMostFrequentFiles(limit: number = 10): RecentFileEntry[] {
    const entries = this.getRecentFiles()
    entries.sort((a, b) => b.accessCount - a.accessCount)
    return entries.slice(0, limit)
  }

  /**
   * Remove a recent file entry
   */
  removeRecentFile(fileId: string): void {
    const key = Array.from(this.recentFiles.keys()).find(k => 
      this.recentFiles.get(k)?.file.id === fileId
    )
    
    if (key) {
      this.recentFiles.delete(key)
      this.saveRecentFiles()
    }
  }

  /**
   * Clear all recent files
   */
  clearRecentFiles(): void {
    this.recentFiles.clear()
    this.saveRecentFiles()
  }

  /**
   * Clear recent files for a specific project
   */
  clearProjectRecentFiles(projectPath: string): void {
    const keysToDelete: string[] = []
    
    this.recentFiles.forEach((entry, key) => {
      if (entry.projectPath === projectPath) {
        keysToDelete.push(key)
      }
    })
    
    keysToDelete.forEach(key => this.recentFiles.delete(key))
    this.saveRecentFiles()
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<RecentFilesConfig>): void {
    this.config = { ...this.config, ...newConfig }
    this.saveConfig()
    
    if (newConfig.maxRecentFiles) {
      this.enforceMaxFiles()
    }
    
    if (newConfig.autoCleanup !== undefined) {
      this.setupAutoCleanup()
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): RecentFilesConfig {
    return { ...this.config }
  }

  /**
   * Get statistics about recent files
   */
  getStatistics(): {
    totalFiles: number
    totalAccesses: number
    averageAccessCount: number
    oldestEntry: Date | null
    newestEntry: Date | null
    projectCount: number
    operationCounts: Record<string, number>
  } {
    const entries = Array.from(this.recentFiles.values())
    
    if (entries.length === 0) {
      return {
        totalFiles: 0,
        totalAccesses: 0,
        averageAccessCount: 0,
        oldestEntry: null,
        newestEntry: null,
        projectCount: 0,
        operationCounts: {}
      }
    }

    const totalAccesses = entries.reduce((sum, entry) => sum + entry.accessCount, 0)
    const projects = new Set(entries.map(e => e.projectPath).filter(Boolean))
    const operationCounts: Record<string, number> = {}
    
    entries.forEach(entry => {
      operationCounts[entry.operation] = (operationCounts[entry.operation] || 0) + 1
    })

    const dates = entries.map(e => e.lastAccessed)
    const oldestEntry = new Date(Math.min(...dates.map(d => d.getTime())))
    const newestEntry = new Date(Math.max(...dates.map(d => d.getTime())))

    return {
      totalFiles: entries.length,
      totalAccesses,
      averageAccessCount: totalAccesses / entries.length,
      oldestEntry,
      newestEntry,
      projectCount: projects.size,
      operationCounts
    }
  }

  /**
   * Search recent files
   */
  searchRecentFiles(query: string): RecentFileEntry[] {
    const lowerQuery = query.toLowerCase()
    return this.getRecentFiles().filter(entry => 
      entry.file.name.toLowerCase().includes(lowerQuery) ||
      (entry.file.path && entry.file.path.toLowerCase().includes(lowerQuery)) ||
      (entry.projectPath && entry.projectPath.toLowerCase().includes(lowerQuery))
    )
  }

  /**
   * Check if file exists in recent files
   */
  isRecentFile(file: FileSystemItem): boolean {
    const key = this.getFileKey(file)
    return this.recentFiles.has(key)
  }

  /**
   * Get file key for storage
   */
  private getFileKey(file: FileSystemItem): string {
    return `${file.path || file.name}-${file.type}`
  }

  /**
   * Generate unique entry ID
   */
  private generateEntryId(): string {
    return `recent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Enforce maximum number of recent files
   */
  private enforceMaxFiles(): void {
    if (this.recentFiles.size <= this.config.maxRecentFiles) {
      return
    }

    const entries = Array.from(this.recentFiles.entries())
    entries.sort(([, a], [, b]) => a.lastAccessed.getTime() - b.lastAccessed.getTime())

    const toRemove = entries.slice(0, this.recentFiles.size - this.config.maxRecentFiles)
    toRemove.forEach(([key]) => this.recentFiles.delete(key))
  }

  /**
   * Get days since a date
   */
  private getDaysSince(date: Date): number {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  /**
   * Setup automatic cleanup
   */
  private setupAutoCleanup(): void {
    if (!this.config.autoCleanup) {
      return
    }

    // Run cleanup every hour
    setInterval(() => {
      this.cleanupExpiredEntries()
    }, 60 * 60 * 1000)
  }

  /**
   * Clean up expired entries
   */
  private cleanupExpiredEntries(): void {
    const keysToDelete: string[] = []
    
    this.recentFiles.forEach((entry, key) => {
      const daysSinceAccess = this.getDaysSince(entry.lastAccessed)
      if (daysSinceAccess > this.config.maxAge) {
        keysToDelete.push(key)
      }
    })
    
    if (keysToDelete.length > 0) {
      keysToDelete.forEach(key => this.recentFiles.delete(key))
      this.saveRecentFiles()
    }
  }

  /**
   * Save recent files to localStorage
   */
  private saveRecentFiles(): void {
    try {
      const data = Array.from(this.recentFiles.entries()).map(([key, entry]) => ({
        key,
        entry: {
          ...entry,
          lastAccessed: entry.lastAccessed.toISOString()
        }
      }))
      
      localStorage.setItem('recentFiles', JSON.stringify(data))
    } catch (error) {
      console.warn('Failed to save recent files:', error)
    }
  }

  /**
   * Load recent files from localStorage
   */
  private loadRecentFiles(): void {
    try {
      const saved = localStorage.getItem('recentFiles')
      if (saved) {
        const data = JSON.parse(saved)
        
        data.forEach(({ key, entry }: any) => {
          this.recentFiles.set(key, {
            ...entry,
            lastAccessed: new Date(entry.lastAccessed)
          })
        })
        
        // Clean up expired entries on load
        this.cleanupExpiredEntries()
      }
    } catch (error) {
      console.warn('Failed to load recent files:', error)
    }
  }

  /**
   * Save configuration to localStorage
   */
  private saveConfig(): void {
    try {
      localStorage.setItem('recentFilesConfig', JSON.stringify(this.config))
    } catch (error) {
      console.warn('Failed to save recent files config:', error)
    }
  }

  /**
   * Load configuration from localStorage
   */
  private loadConfig(): void {
    try {
      const saved = localStorage.getItem('recentFilesConfig')
      if (saved) {
        const config = JSON.parse(saved)
        this.config = { ...this.config, ...config }
      }
    } catch (error) {
      console.warn('Failed to load recent files config:', error)
    }
  }
}
