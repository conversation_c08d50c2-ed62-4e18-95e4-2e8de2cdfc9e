/**
 * Recent Files Demo Component
 * Demonstrates recent files functionality with interactive examples
 */

import React, { useState } from 'react'
import { Clock, BarChart3, Play, Eye, Edit, Plus, Trash2 } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from "@/lib/utils"
import { FileSystemItem } from '../types'
import { useRecentFiles, useRecentFilesUI, useRecentFilesTracking } from './useRecentFiles'
import { RecentFilesSection, RecentFilesQuickAccess } from './RecentFilesSection'

// Sample files for demonstration
const sampleFiles: FileSystemItem[] = [
  { id: 'file1', name: 'App.tsx', type: 'tsx', path: '/project/src/App.tsx' },
  { id: 'file2', name: 'index.ts', type: 'ts', path: '/project/src/index.ts' },
  { id: 'file3', name: 'styles.css', type: 'css', path: '/project/src/styles.css' },
  { id: 'file4', name: 'README.md', type: 'md', path: '/project/README.md' },
  { id: 'file5', name: 'package.json', type: 'json', path: '/project/package.json' },
  { id: 'file6', name: 'config.yaml', type: 'yaml', path: '/project/config.yaml' },
  { id: 'file7', name: 'logo.png', type: 'png', path: '/project/assets/logo.png' },
  { id: 'file8', name: 'utils.js', type: 'js', path: '/project/src/utils.js' },
  { id: 'file9', name: 'test.spec.ts', type: 'ts', path: '/project/tests/test.spec.ts' },
  { id: 'file10', name: 'database.sql', type: 'sql', path: '/project/db/database.sql' }
]

export const RecentFilesDemo: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<FileSystemItem | null>(null)
  const [simulationRunning, setSimulationRunning] = useState(false)

  const recentFiles = useRecentFiles({
    autoTrack: true,
    maxDisplay: 20,
    groupByProject: true
  })

  const recentFilesTracking = useRecentFilesTracking()
  const uiHelpers = useRecentFilesUI(recentFiles.recentFiles)

  // Simulate file operations
  const simulateFileOperation = (operation: 'opened' | 'modified' | 'created' | 'viewed') => {
    const randomFile = sampleFiles[Math.floor(Math.random() * sampleFiles.length)]
    recentFilesTracking[`trackFile${operation.charAt(0).toUpperCase() + operation.slice(1)}`](
      randomFile, 
      '/project'
    )
  }

  const runSimulation = async () => {
    setSimulationRunning(true)
    
    // Simulate a series of file operations
    const operations: Array<'opened' | 'modified' | 'created' | 'viewed'> = [
      'opened', 'viewed', 'modified', 'opened', 'viewed', 'created', 'opened', 'modified'
    ]
    
    for (let i = 0; i < operations.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 500))
      simulateFileOperation(operations[i])
    }
    
    setSimulationRunning(false)
  }

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Clock className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-bold">Recent Files Demo</h2>
        </div>
        <p className="text-muted-foreground">
          Explore recent files tracking and management functionality
        </p>
      </div>

      {/* Demo Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="w-4 h-4" />
            Demo Controls
          </CardTitle>
          <CardDescription>
            Simulate file operations to see recent files in action
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2 flex-wrap">
            <Button
              onClick={() => simulateFileOperation('opened')}
              className="gap-1"
              size="sm"
            >
              <Eye className="w-3 h-3" />
              Open File
            </Button>
            
            <Button
              onClick={() => simulateFileOperation('modified')}
              className="gap-1"
              size="sm"
              variant="outline"
            >
              <Edit className="w-3 h-3" />
              Modify File
            </Button>
            
            <Button
              onClick={() => simulateFileOperation('created')}
              className="gap-1"
              size="sm"
              variant="outline"
            >
              <Plus className="w-3 h-3" />
              Create File
            </Button>
            
            <Button
              onClick={() => simulateFileOperation('viewed')}
              className="gap-1"
              size="sm"
              variant="outline"
            >
              <Eye className="w-3 h-3" />
              View File
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              onClick={runSimulation}
              disabled={simulationRunning}
              className="gap-1"
            >
              <Play className="w-3 h-3" />
              {simulationRunning ? 'Running Simulation...' : 'Run Simulation'}
            </Button>
            
            <Button
              onClick={recentFiles.clearRecentFiles}
              variant="outline"
              className="gap-1"
            >
              <Trash2 className="w-3 h-3" />
              Clear All
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Files Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {recentFiles.statistics.totalFiles}
              </div>
              <div className="text-sm text-muted-foreground">Total Files</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {recentFiles.statistics.totalAccesses}
              </div>
              <div className="text-sm text-muted-foreground">Total Accesses</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {Math.round(recentFiles.statistics.averageAccessCount * 10) / 10}
              </div>
              <div className="text-sm text-muted-foreground">Avg Access Count</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {recentFiles.statistics.projectCount}
              </div>
              <div className="text-sm text-muted-foreground">Projects</div>
            </div>
          </div>

          {/* Operation Breakdown */}
          {Object.keys(recentFiles.statistics.operationCounts).length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Operations</h4>
              <div className="flex items-center gap-2 flex-wrap">
                {Object.entries(recentFiles.statistics.operationCounts).map(([operation, count]) => (
                  <Badge key={operation} variant="outline" className="gap-1">
                    <span className={uiHelpers.getOperationColor(operation)}>
                      {uiHelpers.getOperationIcon(operation)}
                    </span>
                    <span className="capitalize">{operation}</span>
                    <span>{count}</span>
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Files Display */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Full Recent Files Section */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Files Section</CardTitle>
            <CardDescription>
              Full-featured recent files with search and grouping
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RecentFilesSection
              onFileSelect={(file) => {
                setSelectedFile(file)
                recentFilesTracking.trackFileView(file, '/project')
              }}
              onFileOpen={(file) => {
                setSelectedFile(file)
                recentFilesTracking.trackFileOpen(file, '/project')
              }}
              maxItems={15}
              showProjectGroups={false}
              showTimeGroups={true}
            />
          </CardContent>
        </Card>

        {/* Quick Access */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Access</CardTitle>
            <CardDescription>
              Compact recent files for quick navigation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RecentFilesQuickAccess
              maxItems={8}
              onFileSelect={(file) => {
                setSelectedFile(file)
                recentFilesTracking.trackFileView(file, '/project')
              }}
            />
            
            {selectedFile && (
              <div className="mt-4 p-3 border border-border rounded-md bg-muted/20">
                <div className="text-sm font-medium">Selected File</div>
                <div className="text-xs text-muted-foreground mt-1">
                  {selectedFile.name} ({selectedFile.type})
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Most Frequent Files */}
      {recentFiles.mostFrequentFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Most Frequent Files</CardTitle>
            <CardDescription>
              Files you access most often
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {recentFiles.mostFrequentFiles.slice(0, 5).map((entry) => (
                <div
                  key={entry.id}
                  className="flex items-center justify-between p-2 border border-border rounded-md"
                >
                  <div className="flex items-center gap-2">
                    <span>{uiHelpers.getFileTypeIcon(entry.file)}</span>
                    <span className="text-sm font-medium">{entry.file.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {entry.accessCount} accesses
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {uiHelpers.formatRelativeTime(entry.lastAccessed)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* How It Works */}
      <Card>
        <CardHeader>
          <CardTitle>How Recent Files Works</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">📊 Automatic Tracking</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Files are automatically tracked when you interact with them
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• 👁️ Opened: When you open a file</li>
                <li>• ✏️ Modified: When you edit a file</li>
                <li>• ✨ Created: When you create a new file</li>
                <li>• 👀 Viewed: When you preview a file</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">🔍 Smart Organization</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Files are organized by time and frequency
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Time groups: Today, Yesterday, This Week</li>
                <li>• Project groups: Organized by project path</li>
                <li>• Frequency tracking: Most accessed files</li>
                <li>• Search functionality: Find files quickly</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">⚙️ Configuration</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Customize how recent files are tracked and displayed
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Max files: Limit number of tracked files</li>
                <li>• Max age: Auto-cleanup after X days</li>
                <li>• Operations: Choose which operations to track</li>
                <li>• Auto-cleanup: Automatic maintenance</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">💾 Persistence</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Recent files are saved and restored between sessions
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Local storage: Data persists locally</li>
                <li>• Session restore: Files available after restart</li>
                <li>• Privacy: Data stays on your device</li>
                <li>• Performance: Fast access and updates</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default RecentFilesDemo
