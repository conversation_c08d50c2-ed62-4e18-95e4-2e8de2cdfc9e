/**
 * Recent Files Hook
 * React hook for integrating recent files functionality with components
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import { FileSystemItem } from '../types'
import { RecentFilesManager, RecentFileEntry, RecentFilesConfig } from './RecentFilesManager'

export interface RecentFilesOptions {
  autoTrack?: boolean
  maxDisplay?: number
  groupByProject?: boolean
  onFileAccess?: (file: FileSystemItem, operation: string) => void
}

export interface RecentFilesResult {
  recentFiles: RecentFileEntry[]
  recentFilesByProject: Record<string, RecentFileEntry[]>
  mostFrequentFiles: RecentFileEntry[]
  addRecentFile: (file: FileSystemItem, operation: 'opened' | 'modified' | 'created' | 'viewed', projectPath?: string) => void
  removeRecentFile: (fileId: string) => void
  clearRecentFiles: () => void
  clearProjectRecentFiles: (projectPath: string) => void
  searchRecentFiles: (query: string) => RecentFileEntry[]
  isRecentFile: (file: FileSystemItem) => boolean
  updateConfig: (config: Partial<RecentFilesConfig>) => void
  config: RecentFilesConfig
  statistics: any
  isLoading: boolean
}

export const useRecentFiles = (options: RecentFilesOptions = {}): RecentFilesResult => {
  const {
    autoTrack = true,
    maxDisplay = 20,
    groupByProject = true,
    onFileAccess
  } = options

  const [recentFiles, setRecentFiles] = useState<RecentFileEntry[]>([])
  const [config, setConfig] = useState<RecentFilesConfig>({
    maxRecentFiles: 50,
    maxAge: 30,
    trackOperations: ['opened', 'modified', 'created', 'viewed'],
    groupByProject: true,
    autoCleanup: true
  })
  const [isLoading, setIsLoading] = useState(false)
  const [manager] = useState(() => RecentFilesManager.getInstance())

  // Load initial data
  useEffect(() => {
    setIsLoading(true)
    try {
      setRecentFiles(manager.getRecentFiles(maxDisplay))
      setConfig(manager.getConfig())
    } catch (error) {
      console.error('Error loading recent files:', error)
    } finally {
      setIsLoading(false)
    }
  }, [manager, maxDisplay])

  // Group recent files by project
  const recentFilesByProject = useMemo(() => {
    if (!groupByProject) return {}
    return manager.getRecentFilesByProject()
  }, [recentFiles, groupByProject, manager])

  // Get most frequent files
  const mostFrequentFiles = useMemo(() => {
    return manager.getMostFrequentFiles(10)
  }, [recentFiles, manager])

  // Get statistics
  const statistics = useMemo(() => {
    return manager.getStatistics()
  }, [recentFiles, manager])

  /**
   * Add a recent file entry
   */
  const addRecentFile = useCallback((
    file: FileSystemItem, 
    operation: 'opened' | 'modified' | 'created' | 'viewed', 
    projectPath?: string
  ) => {
    if (!autoTrack) return

    try {
      manager.addRecentFile(file, operation, projectPath)
      setRecentFiles(manager.getRecentFiles(maxDisplay))
      onFileAccess?.(file, operation)
    } catch (error) {
      console.error('Error adding recent file:', error)
    }
  }, [manager, maxDisplay, autoTrack, onFileAccess])

  /**
   * Remove a recent file entry
   */
  const removeRecentFile = useCallback((fileId: string) => {
    try {
      manager.removeRecentFile(fileId)
      setRecentFiles(manager.getRecentFiles(maxDisplay))
    } catch (error) {
      console.error('Error removing recent file:', error)
    }
  }, [manager, maxDisplay])

  /**
   * Clear all recent files
   */
  const clearRecentFiles = useCallback(() => {
    try {
      manager.clearRecentFiles()
      setRecentFiles([])
    } catch (error) {
      console.error('Error clearing recent files:', error)
    }
  }, [manager])

  /**
   * Clear recent files for a specific project
   */
  const clearProjectRecentFiles = useCallback((projectPath: string) => {
    try {
      manager.clearProjectRecentFiles(projectPath)
      setRecentFiles(manager.getRecentFiles(maxDisplay))
    } catch (error) {
      console.error('Error clearing project recent files:', error)
    }
  }, [manager, maxDisplay])

  /**
   * Search recent files
   */
  const searchRecentFiles = useCallback((query: string): RecentFileEntry[] => {
    try {
      return manager.searchRecentFiles(query)
    } catch (error) {
      console.error('Error searching recent files:', error)
      return []
    }
  }, [manager])

  /**
   * Check if file is in recent files
   */
  const isRecentFile = useCallback((file: FileSystemItem): boolean => {
    try {
      return manager.isRecentFile(file)
    } catch (error) {
      console.error('Error checking recent file:', error)
      return false
    }
  }, [manager])

  /**
   * Update configuration
   */
  const updateConfig = useCallback((newConfig: Partial<RecentFilesConfig>) => {
    try {
      manager.updateConfig(newConfig)
      setConfig(manager.getConfig())
      setRecentFiles(manager.getRecentFiles(maxDisplay))
    } catch (error) {
      console.error('Error updating config:', error)
    }
  }, [manager, maxDisplay])

  return {
    recentFiles,
    recentFilesByProject,
    mostFrequentFiles,
    addRecentFile,
    removeRecentFile,
    clearRecentFiles,
    clearProjectRecentFiles,
    searchRecentFiles,
    isRecentFile,
    updateConfig,
    config,
    statistics,
    isLoading
  }
}

/**
 * Hook for recent files UI helpers
 */
export const useRecentFilesUI = (recentFiles: RecentFileEntry[]) => {
  /**
   * Format relative time
   */
  const formatRelativeTime = useCallback((date: Date): string => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMinutes < 1) return 'Just now'
    if (diffMinutes < 60) return `${diffMinutes}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}w ago`
    return `${Math.floor(diffDays / 30)}mo ago`
  }, [])

  /**
   * Get operation icon
   */
  const getOperationIcon = useCallback((operation: string): string => {
    const icons: Record<string, string> = {
      opened: '👁️',
      modified: '✏️',
      created: '✨',
      viewed: '👀'
    }
    return icons[operation] || '📄'
  }, [])

  /**
   * Get operation color
   */
  const getOperationColor = useCallback((operation: string): string => {
    const colors: Record<string, string> = {
      opened: 'text-blue-600 dark:text-blue-400',
      modified: 'text-orange-600 dark:text-orange-400',
      created: 'text-green-600 dark:text-green-400',
      viewed: 'text-purple-600 dark:text-purple-400'
    }
    return colors[operation] || 'text-gray-600 dark:text-gray-400'
  }, [])

  /**
   * Group files by time period
   */
  const groupByTimePeriod = useCallback((files: RecentFileEntry[]) => {
    const now = new Date()
    const groups: Record<string, RecentFileEntry[]> = {
      'Today': [],
      'Yesterday': [],
      'This Week': [],
      'This Month': [],
      'Older': []
    }

    files.forEach(file => {
      const diffMs = now.getTime() - file.lastAccessed.getTime()
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

      if (diffDays === 0) {
        groups['Today'].push(file)
      } else if (diffDays === 1) {
        groups['Yesterday'].push(file)
      } else if (diffDays <= 7) {
        groups['This Week'].push(file)
      } else if (diffDays <= 30) {
        groups['This Month'].push(file)
      } else {
        groups['Older'].push(file)
      }
    })

    // Remove empty groups
    Object.keys(groups).forEach(key => {
      if (groups[key].length === 0) {
        delete groups[key]
      }
    })

    return groups
  }, [])

  /**
   * Get file type icon
   */
  const getFileTypeIcon = useCallback((file: FileSystemItem): string => {
    if (file.type === 'folder') return '📁'
    
    const extension = file.name.includes('.') 
      ? file.name.split('.').pop()?.toLowerCase() || ''
      : ''

    const icons: Record<string, string> = {
      'js': '🟨', 'jsx': '⚛️', 'ts': '🔷', 'tsx': '⚛️',
      'html': '🌐', 'css': '🎨', 'scss': '🎨', 'json': '📋',
      'md': '📝', 'txt': '📄', 'png': '🖼️', 'jpg': '🖼️',
      'pdf': '📕', 'zip': '📦', 'mp4': '🎬', 'mp3': '🎵'
    }

    return icons[extension] || '📄'
  }, [])

  return {
    formatRelativeTime,
    getOperationIcon,
    getOperationColor,
    groupByTimePeriod,
    getFileTypeIcon
  }
}

/**
 * Hook for recent files tracking
 */
export const useRecentFilesTracking = () => {
  const { addRecentFile } = useRecentFiles({ autoTrack: true })

  /**
   * Track file open
   */
  const trackFileOpen = useCallback((file: FileSystemItem, projectPath?: string) => {
    addRecentFile(file, 'opened', projectPath)
  }, [addRecentFile])

  /**
   * Track file modification
   */
  const trackFileModification = useCallback((file: FileSystemItem, projectPath?: string) => {
    addRecentFile(file, 'modified', projectPath)
  }, [addRecentFile])

  /**
   * Track file creation
   */
  const trackFileCreation = useCallback((file: FileSystemItem, projectPath?: string) => {
    addRecentFile(file, 'created', projectPath)
  }, [addRecentFile])

  /**
   * Track file view
   */
  const trackFileView = useCallback((file: FileSystemItem, projectPath?: string) => {
    addRecentFile(file, 'viewed', projectPath)
  }, [addRecentFile])

  return {
    trackFileOpen,
    trackFileModification,
    trackFileCreation,
    trackFileView
  }
}
