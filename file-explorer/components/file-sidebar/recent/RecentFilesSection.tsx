/**
 * Recent Files Section Component
 * Displays recently accessed files with grouping and filtering options
 */

import React, { useState } from 'react'
import { Clock, X, Search, Settings, Trash2, FolderO<PERSON>, Eye } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu"
import { FileSystemItem } from '../types'
import { RecentFileEntry } from './RecentFilesManager'
import { useRecentFiles, useRecentFilesUI } from './useRecentFiles'

interface RecentFilesSectionProps {
  onFileSelect?: (file: FileSystemItem) => void
  onFileOpen?: (file: FileSystemItem) => void
  className?: string
  maxItems?: number
  showProjectGroups?: boolean
  showTimeGroups?: boolean
}

export const RecentFilesSection: React.FC<RecentFilesSectionProps> = ({
  onFileSelect,
  onFileOpen,
  className,
  maxItems = 20,
  showProjectGroups = true,
  showTimeGroups = false
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'list' | 'grouped'>('list')
  const [showSettings, setShowSettings] = useState(false)

  const recentFiles = useRecentFiles({
    autoTrack: true,
    maxDisplay: maxItems,
    groupByProject: showProjectGroups
  })

  const uiHelpers = useRecentFilesUI(recentFiles.recentFiles)

  // Filter files based on search query
  const filteredFiles = searchQuery 
    ? recentFiles.searchRecentFiles(searchQuery)
    : recentFiles.recentFiles

  // Group files if needed
  const groupedFiles = showTimeGroups 
    ? uiHelpers.groupByTimePeriod(filteredFiles)
    : null

  const handleFileClick = (file: FileSystemItem) => {
    onFileSelect?.(file)
    recentFiles.addRecentFile(file, 'viewed')
  }

  const handleFileDoubleClick = (file: FileSystemItem) => {
    onFileOpen?.(file)
    recentFiles.addRecentFile(file, 'opened')
  }

  if (recentFiles.isLoading) {
    return (
      <div className={cn("flex items-center justify-center p-4", className)}>
        <div className="text-sm text-muted-foreground">Loading recent files...</div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 text-muted-foreground" />
          <h3 className="text-sm font-medium">Recent Files</h3>
          <Badge variant="outline" className="text-xs">
            {recentFiles.recentFiles.length}
          </Badge>
        </div>

        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <Settings className="w-3 h-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>View Options</DropdownMenuLabel>
              <DropdownMenuCheckboxItem
                checked={showTimeGroups}
                onCheckedChange={(checked) => setViewMode(checked ? 'grouped' : 'list')}
              >
                Group by Time
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={showProjectGroups}
                onCheckedChange={() => {}}
              >
                Group by Project
              </DropdownMenuCheckboxItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={recentFiles.clearRecentFiles}>
                <Trash2 className="w-4 h-4 mr-2" />
                Clear All
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-muted-foreground" />
        <Input
          placeholder="Search recent files..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="h-7 pl-7 text-xs"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSearchQuery('')}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0"
          >
            <X className="w-3 h-3" />
          </Button>
        )}
      </div>

      {/* Files List */}
      <ScrollArea className="h-64">
        {filteredFiles.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">
              {searchQuery ? 'No files match your search' : 'No recent files'}
            </p>
          </div>
        ) : groupedFiles ? (
          <GroupedFilesList
            groupedFiles={groupedFiles}
            onFileClick={handleFileClick}
            onFileDoubleClick={handleFileDoubleClick}
            onRemoveFile={recentFiles.removeRecentFile}
            uiHelpers={uiHelpers}
          />
        ) : (
          <FilesList
            files={filteredFiles}
            onFileClick={handleFileClick}
            onFileDoubleClick={handleFileDoubleClick}
            onRemoveFile={recentFiles.removeRecentFile}
            uiHelpers={uiHelpers}
          />
        )}
      </ScrollArea>

      {/* Statistics */}
      {recentFiles.statistics.totalFiles > 0 && (
        <div className="text-xs text-muted-foreground border-t pt-2">
          <div className="flex items-center justify-between">
            <span>{recentFiles.statistics.totalFiles} total files</span>
            <span>{recentFiles.statistics.totalAccesses} total accesses</span>
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Files List Component
 */
const FilesList: React.FC<{
  files: RecentFileEntry[]
  onFileClick: (file: FileSystemItem) => void
  onFileDoubleClick: (file: FileSystemItem) => void
  onRemoveFile: (fileId: string) => void
  uiHelpers: ReturnType<typeof useRecentFilesUI>
}> = ({ files, onFileClick, onFileDoubleClick, onRemoveFile, uiHelpers }) => {
  return (
    <div className="space-y-1">
      {files.map((entry) => (
        <RecentFileItem
          key={entry.id}
          entry={entry}
          onClick={() => onFileClick(entry.file)}
          onDoubleClick={() => onFileDoubleClick(entry.file)}
          onRemove={() => onRemoveFile(entry.file.id)}
          uiHelpers={uiHelpers}
        />
      ))}
    </div>
  )
}

/**
 * Grouped Files List Component
 */
const GroupedFilesList: React.FC<{
  groupedFiles: Record<string, RecentFileEntry[]>
  onFileClick: (file: FileSystemItem) => void
  onFileDoubleClick: (file: FileSystemItem) => void
  onRemoveFile: (fileId: string) => void
  uiHelpers: ReturnType<typeof useRecentFilesUI>
}> = ({ groupedFiles, onFileClick, onFileDoubleClick, onRemoveFile, uiHelpers }) => {
  return (
    <div className="space-y-3">
      {Object.entries(groupedFiles).map(([group, files]) => (
        <div key={group}>
          <div className="text-xs font-medium text-muted-foreground mb-1 px-1">
            {group}
          </div>
          <div className="space-y-1">
            {files.map((entry) => (
              <RecentFileItem
                key={entry.id}
                entry={entry}
                onClick={() => onFileClick(entry.file)}
                onDoubleClick={() => onFileDoubleClick(entry.file)}
                onRemove={() => onRemoveFile(entry.file.id)}
                uiHelpers={uiHelpers}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

/**
 * Recent File Item Component
 */
const RecentFileItem: React.FC<{
  entry: RecentFileEntry
  onClick: () => void
  onDoubleClick: () => void
  onRemove: () => void
  uiHelpers: ReturnType<typeof useRecentFilesUI>
}> = ({ entry, onClick, onDoubleClick, onRemove, uiHelpers }) => {
  const [showActions, setShowActions] = useState(false)

  return (
    <div
      className="group flex items-center gap-2 p-2 rounded-md hover:bg-accent/50 cursor-pointer transition-colors"
      onClick={onClick}
      onDoubleClick={onDoubleClick}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* File Icon */}
      <span className="text-sm flex-shrink-0">
        {uiHelpers.getFileTypeIcon(entry.file)}
      </span>

      {/* File Info */}
      <div className="flex-1 min-w-0">
        <div className="text-xs font-medium truncate">
          {entry.file.name}
        </div>
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span className={uiHelpers.getOperationColor(entry.operation)}>
            {uiHelpers.getOperationIcon(entry.operation)}
          </span>
          <span>{uiHelpers.formatRelativeTime(entry.lastAccessed)}</span>
          {entry.accessCount > 1 && (
            <Badge variant="outline" className="text-xs h-3 px-1">
              {entry.accessCount}x
            </Badge>
          )}
        </div>
      </div>

      {/* Actions */}
      {showActions && (
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              onRemove()
            }}
            className="h-5 w-5 p-0"
            title="Remove from recent files"
          >
            <X className="w-3 h-3" />
          </Button>
        </div>
      )}
    </div>
  )
}

/**
 * Recent Files Quick Access Component
 */
interface RecentFilesQuickAccessProps {
  maxItems?: number
  onFileSelect?: (file: FileSystemItem) => void
  className?: string
}

export const RecentFilesQuickAccess: React.FC<RecentFilesQuickAccessProps> = ({
  maxItems = 5,
  onFileSelect,
  className
}) => {
  const recentFiles = useRecentFiles({ maxDisplay: maxItems })
  const uiHelpers = useRecentFilesUI(recentFiles.recentFiles)

  if (recentFiles.recentFiles.length === 0) {
    return null
  }

  return (
    <div className={cn("space-y-1", className)}>
      <div className="text-xs font-medium text-muted-foreground px-1">
        Recent Files
      </div>
      {recentFiles.recentFiles.slice(0, maxItems).map((entry) => (
        <div
          key={entry.id}
          className="flex items-center gap-2 p-1 rounded hover:bg-accent/50 cursor-pointer text-xs"
          onClick={() => onFileSelect?.(entry.file)}
        >
          <span>{uiHelpers.getFileTypeIcon(entry.file)}</span>
          <span className="truncate flex-1">{entry.file.name}</span>
          <span className="text-muted-foreground">
            {uiHelpers.formatRelativeTime(entry.lastAccessed)}
          </span>
        </div>
      ))}
    </div>
  )
}

export default RecentFilesSection
