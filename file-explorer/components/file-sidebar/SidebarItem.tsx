/**
 * Sidebar Item Component
 * Renders individual file or folder items in the sidebar with enhanced context menu support
 */

import React, { useCallback, useState } from "react"
import { ChevronDown, ChevronRight, Folder, FolderOpen } from "lucide-react"
import { cn } from "@/lib/utils"
import { CodeFileIcon } from "./CodeFileIcon"
import { SidebarItemProps } from "./types"
import {
  FileContextMenu,
  FolderContextMenu,
  ContextMenuAction,
  ContextMenuActionHandler,
  getClipboardManager
} from "./context-menu"
import {
  ErrorBadge,
  useErrorStateManager,
  FileErrorState,
  FolderErrorState
} from "./error-indicators"
import { useDraggable, useDropTarget } from "./drag-drop/useDragDrop"
import { HoverPreview } from "./preview/HoverPreview"

export const SidebarItem = ({
  item,
  level = 0,
  onToggle,
  onSelect,
  selectedFile,
  showFileIcons = true
}: SidebarItemProps) => {
  const isFolder = item.type === "folder" || Array.isArray(item.files)
  const indent = level * 16
  const isSelected = selectedFile && selectedFile.id === item.id

  // Error state management
  const errorStateManager = useErrorStateManager()
  const [errorState, setErrorState] = useState<FileErrorState | FolderErrorState | null>(null)

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean
    position: { x: number; y: number }
  }>({
    visible: false,
    position: { x: 0, y: 0 }
  })

  // Subscribe to error state changes
  React.useEffect(() => {
    if (!item.path) return

    let unsubscribe: (() => void) | undefined

    if (isFolder) {
      unsubscribe = errorStateManager.subscribeToFolder(item.path, (state) => {
        setErrorState(state)
      })
    } else {
      unsubscribe = errorStateManager.subscribeToFile(item.path, (state) => {
        setErrorState(state)
      })
    }

    return unsubscribe
  }, [item.path, isFolder, errorStateManager])

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isFolder) {
      onToggle(item.id)
    } else {
      onSelect(item)
    }
  }

  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    setContextMenu({
      visible: true,
      position: { x: e.clientX, y: e.clientY }
    })
  }, [])

  const handleContextMenuAction = useCallback(async (action: ContextMenuAction) => {
    const clipboardManager = getClipboardManager()
    const actionHandler = new ContextMenuActionHandler(clipboardManager)

    try {
      const result = await actionHandler.execute(action, item)

      if (result.success) {
        // Check if this was a cancellation or no-change operation
        const isCancellation = result.data?.action?.includes('cancelled') || result.data?.action?.includes('no-change')

        if (!isCancellation) {
          console.log(`✅ ${action} completed successfully for ${item.name}`)
        }

        // Handle UI-triggering actions
        if (result.data?.triggerUI) {
          switch (action) {
            case 'rename':
              // Trigger rename UI
              console.log('Triggering rename UI for:', item.name)
              break
            case 'properties':
              // Trigger properties dialog
              console.log('Triggering properties dialog for:', item.name)
              break
            case 'new-file':
              // Trigger new file dialog
              console.log('Triggering new file dialog in:', item.path)
              break
            case 'new-folder':
              // Trigger new folder dialog
              console.log('Triggering new folder dialog in:', item.path)
              break
          }
        }
      } else {
        console.error(`❌ ${action} failed for ${item.name}:`, result.error)
      }
    } catch (error) {
      console.error(`❌ Error executing ${action}:`, error)
    }

    setContextMenu({ visible: false, position: { x: 0, y: 0 } })
  }, [item])

  // Drag and drop handlers
  const draggableProps = useDraggable(item, {
    onFileMove: (source, target) => {
      console.log(`File moved: ${source.name} to ${target.name}`)
    },
    onFileCopy: (source, target) => {
      console.log(`File copied: ${source.name} to ${target.name}`)
    },
    onFileReorder: (item, newIndex) => {
      console.log(`File reordered: ${item.name} to index ${newIndex}`)
    }
  })

  const dropTargetProps = useDropTarget(item, {
    onFileMove: (source, target) => {
      console.log(`File moved: ${source.name} to ${target.name}`)
    },
    onFileCopy: (source, target) => {
      console.log(`File copied: ${source.name} to ${target.name}`)
    }
  })



  // Generate dynamic styles based on error state
  const itemStyles = React.useMemo(() => {
    if (!errorState?.hasErrors) return {}

    const theme = 'light' // TODO: Get from theme context
    const severity = errorState.severity

    // Subtle border and background for files/folders with errors
    const borderColors = {
      error: theme === 'light' ? '#FCA5A5' : '#DC2626',
      warning: theme === 'light' ? '#FCD34D' : '#D97706',
      info: theme === 'light' ? '#93C5FD' : '#2563EB',
      hint: theme === 'light' ? '#6EE7B7' : '#059669'
    }

    const backgroundColors = {
      error: theme === 'light' ? '#FEF2F2' : '#7F1D1D20',
      warning: theme === 'light' ? '#FFFBEB' : '#92400E20',
      info: theme === 'light' ? '#EFF6FF' : '#1E3A8A20',
      hint: theme === 'light' ? '#ECFDF5' : '#064E3B20'
    }

    return {
      borderLeft: `2px solid ${borderColors[severity]}`,
      backgroundColor: isSelected ? undefined : backgroundColors[severity]
    }
  }, [errorState, isSelected])

  return (
    <>
      <HoverPreview item={item} delay={600} disabled={isFolder}>
        <div
          className={cn(
            "file-explorer-item rounded-sm cursor-pointer group",
            isSelected && "selected",
            errorState?.hasErrors && "border-l-2",
            draggableProps.className
          )}
          style={{
            paddingLeft: `${indent + 12}px`, // Slightly more indentation for better hierarchy
            ...itemStyles
          }}
          onClick={handleClick}
          onContextMenu={handleContextMenu}
          data-item-id={item.id}
          data-item-path={item.path || ''}
          data-item-type={item.type}
          data-item-name={item.name}
          {...draggableProps}
          {...dropTargetProps}
        >
        {/* Expand/Collapse Icon */}
        {isFolder ? (
          item.expanded ? (
            <ChevronDown className="file-explorer-icon mr-2 text-muted-foreground transition-transform duration-150" />
          ) : (
            <ChevronRight className="file-explorer-icon mr-2 text-muted-foreground transition-transform duration-150" />
          )
        ) : (
          <div className="w-4 mr-2 flex-shrink-0" />
        )}

        {/* File/Folder Icon */}
        {showFileIcons && (
          <>
            {isFolder ? (
              item.expanded ? (
                <FolderOpen className="file-explorer-icon mr-2 text-blue-500/80" />
              ) : (
                <Folder className="file-explorer-icon mr-2 text-blue-500/80" />
              )
            ) : (
              <CodeFileIcon extension={item.type} className="mr-2 flex-shrink-0" size="sm" />
            )}
          </>
        )}

        {/* File/Folder Name */}
        <span className="file-explorer-text">{item.name}</span>

        {/* Error Badge */}
        {errorState && errorState.hasErrors && (
          <ErrorBadge
            errorState={errorState}
            size="xs"
            position="trailing"
            showCount={true}
            showTooltip={true}
            animated={true}
          />
        )}


        </div>
      </HoverPreview>

      {/* Render Children */}
      {isFolder &&
        item.expanded &&
        item.files &&
        item.files.map((file) => (
          <SidebarItem
            key={file.id}
            item={file}
            level={level + 1}
            onToggle={onToggle}
            onSelect={onSelect}
            selectedFile={selectedFile}
            showFileIcons={showFileIcons}
          />
        ))}

      {/* Context Menu */}
      {contextMenu.visible && (
        isFolder ? (
          <FolderContextMenu
            item={item}
            position={contextMenu.position}
            onAction={handleContextMenuAction}
            onClose={() => setContextMenu({ visible: false, position: { x: 0, y: 0 } })}
            visible={contextMenu.visible}
          />
        ) : (
          <FileContextMenu
            item={item}
            position={contextMenu.position}
            onAction={handleContextMenuAction}
            onClose={() => setContextMenu({ visible: false, position: { x: 0, y: 0 } })}
            visible={contextMenu.visible}
          />
        )
      )}
    </>
  )
}
