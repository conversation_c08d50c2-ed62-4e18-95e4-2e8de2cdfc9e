/**
 * File Search Component
 * Main search interface with fuzzy matching and keyboard navigation
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Search, X } from 'lucide-react'
import { cn } from "@/lib/utils"
import { FileSystemItem } from '../types'
import { fuzzySearch, SearchResult, SearchOptions } from './FuzzySearch'
import { SearchResults, SearchInput } from './SearchResults'

interface FileSearchProps {
  projects: FileSystemItem[]
  onFileSelect: (file: FileSystemItem) => void
  className?: string
  placeholder?: string
  maxResults?: number
  minScore?: number
}

export const FileSearch: React.FC<FileSearchProps> = ({
  projects,
  onFileSelect,
  className,
  placeholder = "Search files...",
  maxResults = 50,
  minScore = 0.1
}) => {
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [searchOptions] = useState<SearchOptions>({
    maxResults,
    minScore,
    caseSensitive: false,
    searchInPath: true
  })

  // Debounced search results
  const searchResults = useMemo(() => {
    if (!query.trim()) return []
    return fuzzySearch(query, projects, searchOptions)
  }, [query, projects, searchOptions])

  // Handle search input changes
  const handleQueryChange = useCallback((newQuery: string) => {
    setQuery(newQuery)
    setIsOpen(newQuery.length > 0)
  }, [])

  // Handle result selection
  const handleResultSelect = useCallback((result: SearchResult) => {
    onFileSelect(result.item)
    setQuery('')
    setIsOpen(false)
  }, [onFileSelect])

  // Handle search close
  const handleClose = useCallback(() => {
    setIsOpen(false)
    setQuery('')
  }, [])

  // Handle input focus
  const handleFocus = useCallback(() => {
    if (query.length > 0) {
      setIsOpen(true)
    }
  }, [query])

  // Handle input blur (with delay to allow for result clicks)
  const handleBlur = useCallback(() => {
    setTimeout(() => {
      setIsOpen(false)
    }, 150)
  }, [])

  // Close on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, handleClose])

  return (
    <div className={cn("relative", className)}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
        <input
          type="text"
          value={query}
          onChange={(e) => handleQueryChange(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className="w-full pl-9 pr-8 h-8 bg-background border-input text-sm leading-5 placeholder:text-muted-foreground/70 rounded-md border focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
        />
        
        {/* Clear button */}
        {query && (
          <button
            onClick={handleClose}
            className="absolute right-2 top-1/2 -translate-y-1/2 p-0.5 rounded-sm hover:bg-accent text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="h-3 w-3" />
          </button>
        )}

        {/* Keyboard shortcut hint */}
        {!query && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground/50 font-mono">
            Ctrl+P
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-background border border-border rounded-md shadow-lg overflow-hidden">
          <SearchResults
            results={searchResults}
            onSelect={handleResultSelect}
            onClose={handleClose}
            maxHeight={400}
          />
        </div>
      )}
    </div>
  )
}

/**
 * Quick Search Modal Component
 * Full-screen search overlay similar to VS Code's Ctrl+P
 */
interface QuickSearchProps {
  projects: FileSystemItem[]
  onFileSelect: (file: FileSystemItem) => void
  isOpen: boolean
  onClose: () => void
}

export const QuickSearch: React.FC<QuickSearchProps> = ({
  projects,
  onFileSelect,
  isOpen,
  onClose
}) => {
  const [query, setQuery] = useState('')

  // Search results
  const searchResults = useMemo(() => {
    if (!query.trim()) return []
    return fuzzySearch(query, projects, {
      maxResults: 100,
      minScore: 0.05,
      caseSensitive: false,
      searchInPath: true
    })
  }, [query, projects])

  // Handle result selection
  const handleResultSelect = useCallback((result: SearchResult) => {
    onFileSelect(result.item)
    setQuery('')
    onClose()
  }, [onFileSelect, onClose])

  // Handle close
  const handleClose = useCallback(() => {
    setQuery('')
    onClose()
  }, [onClose])

  // Reset query when modal opens
  useEffect(() => {
    if (isOpen) {
      setQuery('')
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
      <div className="flex items-start justify-center pt-[10vh] px-4">
        <div className="w-full max-w-2xl bg-background border border-border rounded-lg shadow-xl overflow-hidden">
          {/* Search Input */}
          <div className="p-4 border-b border-border">
            <SearchInput
              value={query}
              onChange={setQuery}
              onBlur={handleClose}
              placeholder="Search files across all projects..."
              autoFocus
            />
          </div>

          {/* Search Results */}
          <div className="max-h-96 overflow-hidden">
            <SearchResults
              results={searchResults}
              onSelect={handleResultSelect}
              onClose={handleClose}
              maxHeight={384}
            />
          </div>

          {/* Footer */}
          <div className="px-4 py-2 bg-muted/30 border-t border-border text-xs text-muted-foreground">
            <div className="flex items-center justify-between">
              <span>Use ↑↓ to navigate, Enter to select, Esc to close</span>
              <span>{searchResults.length} results</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FileSearch
