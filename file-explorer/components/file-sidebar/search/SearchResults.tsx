/**
 * Search Results Component
 * Displays fuzzy search results with highlighting and keyboard navigation
 */

import React, { useState, useEffect, useCallback } from 'react'
import { File, Folder, ChevronRight } from 'lucide-react'
import { cn } from "@/lib/utils"
import { SearchResult, highlightMatches } from './FuzzySearch'
import { CodeFileIcon } from '../CodeFileIcon'

interface SearchResultsProps {
  results: SearchResult[]
  onSelect: (result: SearchResult) => void
  onClose: () => void
  maxHeight?: number
}

export const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  onSelect,
  onClose,
  maxHeight = 400
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0)

  // Reset selection when results change
  useEffect(() => {
    setSelectedIndex(0)
  }, [results])

  // Keyboard navigation
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => Math.min(prev + 1, results.length - 1))
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => Math.max(prev - 1, 0))
        break
      case 'Enter':
        e.preventDefault()
        if (results[selectedIndex]) {
          onSelect(results[selectedIndex])
        }
        break
      case 'Escape':
        e.preventDefault()
        onClose()
        break
    }
  }, [results, selectedIndex, onSelect, onClose])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  if (results.length === 0) {
    return (
      <div className="px-3 py-4 text-center text-muted-foreground text-sm">
        No files found
      </div>
    )
  }

  return (
    <div 
      className="border-t border-border bg-background"
      style={{ maxHeight }}
    >
      <div className="px-3 py-2 text-xs font-medium text-muted-foreground border-b border-border">
        {results.length} result{results.length !== 1 ? 's' : ''}
      </div>
      
      <div className="overflow-y-auto file-explorer-scroll">
        {results.map((result, index) => (
          <SearchResultItem
            key={`${result.item.id}-${index}`}
            result={result}
            isSelected={index === selectedIndex}
            onClick={() => onSelect(result)}
            onMouseEnter={() => setSelectedIndex(index)}
          />
        ))}
      </div>
    </div>
  )
}

interface SearchResultItemProps {
  result: SearchResult
  isSelected: boolean
  onClick: () => void
  onMouseEnter: () => void
}

const SearchResultItem: React.FC<SearchResultItemProps> = ({
  result,
  isSelected,
  onClick,
  onMouseEnter
}) => {
  const { item, matches, path, score } = result

  // Create highlighted filename
  const highlightedName = highlightMatches(item.name, matches)
  
  // Get file extension for icon
  const extension = item.name.includes('.') 
    ? item.name.split('.').pop() || '' 
    : item.type

  return (
    <div
      className={cn(
        "file-explorer-item px-3 py-2 cursor-pointer group border-l-2",
        isSelected && "selected bg-accent text-accent-foreground"
      )}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
    >
      <div className="flex items-center gap-2 min-w-0">
        {/* File Icon */}
        <CodeFileIcon 
          extension={extension} 
          className="flex-shrink-0" 
          size="sm" 
        />

        {/* File Info */}
        <div className="flex-1 min-w-0">
          {/* Filename with highlighting */}
          <div 
            className="file-explorer-text font-medium"
            dangerouslySetInnerHTML={{ __html: highlightedName }}
          />
          
          {/* File path */}
          <div className="text-xs text-muted-foreground/70 truncate mt-0.5">
            {path}
          </div>
        </div>

        {/* Score indicator (for debugging) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="text-xs text-muted-foreground/50 font-mono">
            {Math.round(score * 100)}%
          </div>
        )}

        {/* Selection indicator */}
        {isSelected && (
          <ChevronRight className="w-3 h-3 text-muted-foreground flex-shrink-0" />
        )}
      </div>
    </div>
  )
}

/**
 * Search Input Component with enhanced features
 */
interface SearchInputProps {
  value: string
  onChange: (value: string) => void
  onFocus?: () => void
  onBlur?: () => void
  placeholder?: string
  autoFocus?: boolean
}

export const SearchInput: React.FC<SearchInputProps> = ({
  value,
  onChange,
  onFocus,
  onBlur,
  placeholder = "Search files...",
  autoFocus = false
}) => {
  const inputRef = React.useRef<HTMLInputElement>(null)

  // Auto-focus on mount if requested
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  // Global keyboard shortcut (Ctrl+P or Cmd+P)
  useEffect(() => {
    const handleGlobalShortcut = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault()
        inputRef.current?.focus()
      }
    }

    document.addEventListener('keydown', handleGlobalShortcut)
    return () => document.removeEventListener('keydown', handleGlobalShortcut)
  }, [])

  return (
    <div className="relative">
      <input
        ref={inputRef}
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onFocus={onFocus}
        onBlur={onBlur}
        placeholder={placeholder}
        className="w-full pl-9 pr-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent placeholder:text-muted-foreground/70"
      />
      
      {/* Search icon */}
      <div className="absolute left-3 top-1/2 -translate-y-1/2">
        <File className="w-3.5 h-3.5 text-muted-foreground" />
      </div>

      {/* Keyboard shortcut hint */}
      {!value && (
        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground/50 font-mono">
          Ctrl+P
        </div>
      )}
    </div>
  )
}

export default SearchResults
