/**
 * Advanced Fuzzy Search Implementation
 * Provides fuzzy matching, ranking, and highlighting for file search
 */

import { FileSystemItem } from '../types'

export interface SearchResult {
  item: FileSystemItem
  score: number
  matches: SearchMatch[]
  path: string
}

export interface SearchMatch {
  start: number
  end: number
  text: string
}

export interface SearchOptions {
  maxResults?: number
  minScore?: number
  includeContent?: boolean
  caseSensitive?: boolean
  searchInPath?: boolean
}

/**
 * Calculate fuzzy match score between query and text
 * Returns score between 0-1, where 1 is perfect match
 */
export function calculateFuzzyScore(query: string, text: string, caseSensitive = false): number {
  if (!query || !text) return 0

  const q = caseSensitive ? query : query.toLowerCase()
  const t = caseSensitive ? text : text.toLowerCase()

  // Exact match gets highest score
  if (t === q) return 1.0

  // Starts with query gets high score
  if (t.startsWith(q)) return 0.9

  // Contains query as substring gets good score
  if (t.includes(q)) return 0.7

  // Fuzzy matching - check if all characters in query appear in order
  let queryIndex = 0
  let matches = 0
  
  for (let i = 0; i < t.length && queryIndex < q.length; i++) {
    if (t[i] === q[queryIndex]) {
      matches++
      queryIndex++
    }
  }

  if (queryIndex === q.length) {
    // All characters found in order
    const ratio = matches / Math.max(q.length, t.length)
    const consecutiveBonus = calculateConsecutiveBonus(q, t)
    return Math.min(0.6 * ratio + consecutiveBonus, 0.65)
  }

  return 0
}

/**
 * Calculate bonus for consecutive character matches
 */
function calculateConsecutiveBonus(query: string, text: string): number {
  let maxConsecutive = 0
  let currentConsecutive = 0
  let queryIndex = 0

  for (let i = 0; i < text.length && queryIndex < query.length; i++) {
    if (text[i] === query[queryIndex]) {
      currentConsecutive++
      queryIndex++
      maxConsecutive = Math.max(maxConsecutive, currentConsecutive)
    } else {
      currentConsecutive = 0
    }
  }

  return (maxConsecutive / query.length) * 0.1
}

/**
 * Find match positions for highlighting
 */
export function findMatches(query: string, text: string, caseSensitive = false): SearchMatch[] {
  if (!query || !text) return []

  const q = caseSensitive ? query : query.toLowerCase()
  const t = caseSensitive ? text : text.toLowerCase()
  const matches: SearchMatch[] = []

  // Find exact substring matches first
  let index = t.indexOf(q)
  while (index !== -1) {
    matches.push({
      start: index,
      end: index + q.length,
      text: text.substring(index, index + q.length)
    })
    index = t.indexOf(q, index + 1)
  }

  // If no exact matches, find fuzzy matches
  if (matches.length === 0) {
    let queryIndex = 0
    for (let i = 0; i < t.length && queryIndex < q.length; i++) {
      if (t[i] === q[queryIndex]) {
        matches.push({
          start: i,
          end: i + 1,
          text: text[i]
        })
        queryIndex++
      }
    }
  }

  return matches
}

/**
 * Build file path string for search
 */
function buildFilePath(item: FileSystemItem, projects: FileSystemItem[]): string {
  // Find the project this item belongs to
  for (const project of projects) {
    const path = findItemPath(item, project, project.name)
    if (path) return path
  }
  return item.name
}

/**
 * Recursively find path to an item
 */
function findItemPath(target: FileSystemItem, current: FileSystemItem, currentPath: string): string | null {
  if (current.id === target.id) {
    return currentPath
  }

  if (current.files) {
    for (const child of current.files) {
      const path = findItemPath(target, child, `${currentPath}/${child.name}`)
      if (path) return path
    }
  }

  return null
}

/**
 * Recursively collect all files from projects
 */
function collectAllFiles(items: FileSystemItem[], projects: FileSystemItem[]): Array<{item: FileSystemItem, path: string}> {
  const files: Array<{item: FileSystemItem, path: string}> = []

  function traverse(items: FileSystemItem[], currentPath: string = '') {
    for (const item of items) {
      const itemPath = currentPath ? `${currentPath}/${item.name}` : item.name
      
      if (item.type !== 'folder') {
        files.push({ item, path: itemPath })
      }

      if (item.files) {
        traverse(item.files, itemPath)
      }
    }
  }

  traverse(items)
  return files
}

/**
 * Advanced fuzzy search with ranking and highlighting
 */
export function fuzzySearch(
  query: string,
  projects: FileSystemItem[],
  options: SearchOptions = {}
): SearchResult[] {
  const {
    maxResults = 50,
    minScore = 0.1,
    caseSensitive = false,
    searchInPath = true
  } = options

  if (!query.trim()) return []

  // Collect all files from all projects
  const allFiles = collectAllFiles(projects, projects)
  const results: SearchResult[] = []

  for (const { item, path } of allFiles) {
    // Calculate scores for filename and path
    const nameScore = calculateFuzzyScore(query, item.name, caseSensitive)
    const pathScore = searchInPath ? calculateFuzzyScore(query, path, caseSensitive) * 0.7 : 0
    
    // Use the higher score
    const score = Math.max(nameScore, pathScore)

    if (score >= minScore) {
      // Find matches for highlighting
      const nameMatches = findMatches(query, item.name, caseSensitive)
      const pathMatches = searchInPath ? findMatches(query, path, caseSensitive) : []
      
      results.push({
        item,
        score,
        matches: nameScore >= pathScore ? nameMatches : pathMatches,
        path
      })
    }
  }

  // Sort by score (descending) and limit results
  return results
    .sort((a, b) => b.score - a.score)
    .slice(0, maxResults)
}

/**
 * Highlight search matches in text
 */
export function highlightMatches(text: string, matches: SearchMatch[]): string {
  if (!matches.length) return text

  let result = ''
  let lastIndex = 0

  // Sort matches by start position
  const sortedMatches = [...matches].sort((a, b) => a.start - b.start)

  for (const match of sortedMatches) {
    // Add text before match
    result += text.substring(lastIndex, match.start)
    // Add highlighted match
    result += `<mark class="search-highlight">${match.text}</mark>`
    lastIndex = match.end
  }

  // Add remaining text
  result += text.substring(lastIndex)
  return result
}
