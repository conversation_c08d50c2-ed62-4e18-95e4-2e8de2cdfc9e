/**
 * Operations Feedback Component
 * Displays file operation progress, status, and provides undo functionality
 */

import React, { useState } from 'react'
import { X, Undo, Trash2, Play, Pause, AlertCircle, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from "@/lib/utils"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { FileOperation, OperationProgress } from './FileOperationsManager'
import { useFileOperations, useOperationUI } from './useFileOperations'

interface OperationsFeedbackProps {
  className?: string
  compact?: boolean
  showCompleted?: boolean
  maxItems?: number
}

export const OperationsFeedback: React.FC<OperationsFeedbackProps> = ({
  className,
  compact = false,
  showCompleted = true,
  maxItems = 5
}) => {
  const [expanded, setExpanded] = useState(false)
  
  const fileOps = useFileOperations({
    autoCleanup: true,
    maxDisplayOperations: maxItems * 2
  })
  
  const uiHelpers = useOperationUI()

  const displayOperations = showCompleted 
    ? fileOps.operations.slice(0, maxItems)
    : fileOps.activeOperations.slice(0, maxItems)

  if (displayOperations.length === 0 && !fileOps.canUndo) {
    return null
  }

  if (compact) {
    return (
      <CompactOperationsFeedback
        operations={displayOperations}
        activeCount={fileOps.activeOperations.length}
        canUndo={fileOps.canUndo}
        onUndo={fileOps.undoLastOperation}
        onClear={fileOps.clearCompletedOperations}
        className={className}
      />
    )
  }

  return (
    <Card className={cn("operations-feedback", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm flex items-center gap-2">
            <Play className="w-4 h-4" />
            File Operations
            {fileOps.activeOperations.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {fileOps.activeOperations.length} active
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-1">
            {fileOps.canUndo && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={fileOps.undoLastOperation}
                      className="h-6 w-6 p-0"
                    >
                      <Undo className="w-3 h-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Undo last operation</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={fileOps.clearCompletedOperations}
              className="h-6 w-6 p-0"
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <ScrollArea className="h-48">
          <div className="space-y-2">
            {displayOperations.map((operation) => (
              <OperationItem
                key={operation.id}
                operation={operation}
                progress={fileOps.getOperationProgress(operation.id)}
                onCancel={() => fileOps.cancelOperation(operation.id)}
                uiHelpers={uiHelpers}
              />
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

/**
 * Compact Operations Feedback Component
 */
const CompactOperationsFeedback: React.FC<{
  operations: FileOperation[]
  activeCount: number
  canUndo: boolean
  onUndo: () => Promise<boolean>
  onClear: () => void
  className?: string
}> = ({ operations, activeCount, canUndo, onUndo, onClear, className }) => {
  if (operations.length === 0 && !canUndo) return null

  return (
    <div className={cn("flex items-center gap-2 text-xs", className)}>
      {activeCount > 0 && (
        <Badge variant="secondary" className="text-xs animate-pulse">
          {activeCount} operation{activeCount !== 1 ? 's' : ''} running
        </Badge>
      )}
      
      {operations.length > 0 && (
        <span className="text-muted-foreground">
          {operations.length} operation{operations.length !== 1 ? 's' : ''}
        </span>
      )}
      
      {canUndo && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onUndo}
          className="h-5 px-1 text-xs"
        >
          <Undo className="w-3 h-3 mr-1" />
          Undo
        </Button>
      )}
    </div>
  )
}

/**
 * Individual Operation Item Component
 */
const OperationItem: React.FC<{
  operation: FileOperation
  progress: OperationProgress | null
  onCancel: () => void
  uiHelpers: ReturnType<typeof useOperationUI>
}> = ({ operation, progress, onCancel, uiHelpers }) => {
  const isActive = operation.status === 'running' || operation.status === 'pending'
  const canCancel = operation.status === 'running'

  return (
    <div className="operation-item p-2 border border-border rounded-md">
      {/* Header */}
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center gap-2">
          <span className={uiHelpers.getOperationColor(operation.type)}>
            {uiHelpers.getOperationIcon(operation.type)}
          </span>
          <span className="text-sm font-medium capitalize">
            {operation.type}
          </span>
          <span className={uiHelpers.getStatusColor(operation.status)}>
            {uiHelpers.getStatusIcon(operation.status)}
          </span>
        </div>
        
        <div className="flex items-center gap-1">
          {operation.endTime && (
            <span className="text-xs text-muted-foreground">
              {uiHelpers.formatDuration(operation.startTime, operation.endTime)}
            </span>
          )}
          
          {canCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="h-5 w-5 p-0"
            >
              <X className="w-3 h-3" />
            </Button>
          )}
        </div>
      </div>

      {/* Details */}
      <div className="text-xs text-muted-foreground mb-2">
        {operation.sourceItems.length === 1 ? (
          <span>{operation.sourceItems[0].name}</span>
        ) : (
          <span>{uiHelpers.formatFileCount(operation.sourceItems.length)}</span>
        )}
        
        {operation.targetPath && (
          <span> → {operation.targetPath}</span>
        )}
        
        {operation.targetName && (
          <span> → {operation.targetName}</span>
        )}
      </div>

      {/* Progress */}
      {isActive && (
        <div className="space-y-1">
          <Progress value={operation.progress} className="h-1" />
          
          {progress && (
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{progress.currentFile}</span>
              <span>
                {progress.filesProcessed}/{progress.totalFiles}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Error */}
      {operation.status === 'failed' && operation.error && (
        <div className="flex items-center gap-1 text-xs text-red-600 dark:text-red-400 mt-1">
          <AlertCircle className="w-3 h-3" />
          <span>{operation.error}</span>
        </div>
      )}

      {/* Success */}
      {operation.status === 'completed' && (
        <div className="flex items-center gap-1 text-xs text-green-600 dark:text-green-400 mt-1">
          <CheckCircle className="w-3 h-3" />
          <span>Completed successfully</span>
        </div>
      )}
    </div>
  )
}

/**
 * Operation Progress Toast Component
 */
interface OperationProgressToastProps {
  operation: FileOperation
  progress: OperationProgress | null
  onDismiss: () => void
  className?: string
}

export const OperationProgressToast: React.FC<OperationProgressToastProps> = ({
  operation,
  progress,
  onDismiss,
  className
}) => {
  const uiHelpers = useOperationUI()

  return (
    <div className={cn(
      "operation-toast p-3 bg-background border border-border rounded-md shadow-lg",
      "animate-in slide-in-from-right-full duration-300",
      className
    )}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className={uiHelpers.getOperationColor(operation.type)}>
            {uiHelpers.getOperationIcon(operation.type)}
          </span>
          <span className="text-sm font-medium capitalize">
            {operation.type}
          </span>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onDismiss}
          className="h-5 w-5 p-0"
        >
          <X className="w-3 h-3" />
        </Button>
      </div>

      <div className="text-xs text-muted-foreground mb-2">
        {operation.sourceItems.length === 1 
          ? operation.sourceItems[0].name
          : `${operation.sourceItems.length} files`
        }
      </div>

      {operation.status === 'running' && (
        <div className="space-y-1">
          <Progress value={operation.progress} className="h-1" />
          {progress && (
            <div className="text-xs text-muted-foreground">
              {progress.filesProcessed}/{progress.totalFiles} files
            </div>
          )}
        </div>
      )}

      {operation.status === 'completed' && (
        <div className="text-xs text-green-600 dark:text-green-400">
          ✅ Completed successfully
        </div>
      )}

      {operation.status === 'failed' && (
        <div className="text-xs text-red-600 dark:text-red-400">
          ❌ {operation.error || 'Operation failed'}
        </div>
      )}
    </div>
  )
}

/**
 * Undo Button Component
 */
interface UndoButtonProps {
  canUndo: boolean
  onUndo: () => Promise<boolean>
  className?: string
}

export const UndoButton: React.FC<UndoButtonProps> = ({
  canUndo,
  onUndo,
  className
}) => {
  const [isUndoing, setIsUndoing] = useState(false)

  const handleUndo = async () => {
    setIsUndoing(true)
    try {
      await onUndo()
    } finally {
      setIsUndoing(false)
    }
  }

  if (!canUndo) return null

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            onClick={handleUndo}
            disabled={isUndoing}
            className={cn("gap-1", className)}
          >
            <Undo className="w-3 h-3" />
            {isUndoing ? 'Undoing...' : 'Undo'}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Undo the last file operation</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

export default OperationsFeedback
