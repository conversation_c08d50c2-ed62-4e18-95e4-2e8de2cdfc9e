/**
 * File Operations Manager
 * Handles file operations with real feedback, progress tracking, and undo functionality
 */

import { FileSystemItem } from '../types'

export type OperationType = 'copy' | 'move' | 'delete' | 'rename' | 'create' | 'paste'
export type OperationStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'

export interface FileOperation {
  id: string
  type: OperationType
  status: OperationStatus
  sourceItems: FileSystemItem[]
  targetPath?: string
  targetName?: string
  progress: number
  startTime: Date
  endTime?: Date
  error?: string
  canUndo: boolean
  undoData?: any
}

export interface OperationProgress {
  operationId: string
  currentFile: string
  filesProcessed: number
  totalFiles: number
  bytesProcessed: number
  totalBytes: number
  estimatedTimeRemaining?: number
}

export class FileOperationsManager {
  private static instance: FileOperationsManager
  private operations: Map<string, FileOperation> = new Map()
  private listeners: Map<string, (operation: FileOperation) => void> = new Map()
  private progressListeners: Map<string, (progress: OperationProgress) => void> = new Map()
  private undoStack: FileOperation[] = []
  private maxUndoOperations = 20

  static getInstance(): FileOperationsManager {
    if (!FileOperationsManager.instance) {
      FileOperationsManager.instance = new FileOperationsManager()
    }
    return FileOperationsManager.instance
  }

  /**
   * Start a copy operation
   */
  async copyFiles(sourceItems: FileSystemItem[], targetPath: string): Promise<string> {
    const operation = this.createOperation('copy', sourceItems, targetPath)
    
    try {
      await this.executeOperation(operation, async (op) => {
        // Simulate copy operation with realistic progress
        const totalFiles = this.countFiles(sourceItems)
        let processedFiles = 0
        
        for (const item of sourceItems) {
          await this.copyItem(item, targetPath, op.id, processedFiles, totalFiles)
          processedFiles += this.countFiles([item])
        }
        
        // Store undo data
        op.undoData = {
          type: 'copy',
          copiedItems: sourceItems.map(item => ({
            ...item,
            path: `${targetPath}/${item.name}`
          }))
        }
        op.canUndo = true
      })
    } catch (error) {
      this.failOperation(operation.id, error instanceof Error ? error.message : 'Copy failed')
    }

    return operation.id
  }

  /**
   * Start a move operation
   */
  async moveFiles(sourceItems: FileSystemItem[], targetPath: string): Promise<string> {
    const operation = this.createOperation('move', sourceItems, targetPath)
    
    try {
      await this.executeOperation(operation, async (op) => {
        const totalFiles = this.countFiles(sourceItems)
        let processedFiles = 0
        
        // Store original locations for undo
        const originalLocations = sourceItems.map(item => ({
          item: { ...item },
          originalPath: item.path
        }))
        
        for (const item of sourceItems) {
          await this.moveItem(item, targetPath, op.id, processedFiles, totalFiles)
          processedFiles += this.countFiles([item])
        }
        
        op.undoData = {
          type: 'move',
          originalLocations
        }
        op.canUndo = true
      })
    } catch (error) {
      this.failOperation(operation.id, error instanceof Error ? error.message : 'Move failed')
    }

    return operation.id
  }

  /**
   * Start a delete operation
   */
  async deleteFiles(sourceItems: FileSystemItem[]): Promise<string> {
    const operation = this.createOperation('delete', sourceItems)
    
    try {
      await this.executeOperation(operation, async (op) => {
        const totalFiles = this.countFiles(sourceItems)
        let processedFiles = 0
        
        // Store deleted items for undo
        const deletedItems = sourceItems.map(item => ({ ...item }))
        
        for (const item of sourceItems) {
          await this.deleteItem(item, op.id, processedFiles, totalFiles)
          processedFiles += this.countFiles([item])
        }
        
        op.undoData = {
          type: 'delete',
          deletedItems
        }
        op.canUndo = true
      })
    } catch (error) {
      this.failOperation(operation.id, error instanceof Error ? error.message : 'Delete failed')
    }

    return operation.id
  }

  /**
   * Start a rename operation
   */
  async renameFile(sourceItem: FileSystemItem, newName: string): Promise<string> {
    const operation = this.createOperation('rename', [sourceItem], undefined, newName)
    
    try {
      await this.executeOperation(operation, async (op) => {
        await this.renameItem(sourceItem, newName, op.id)
        
        op.undoData = {
          type: 'rename',
          item: { ...sourceItem },
          originalName: sourceItem.name,
          newName
        }
        op.canUndo = true
      })
    } catch (error) {
      this.failOperation(operation.id, error instanceof Error ? error.message : 'Rename failed')
    }

    return operation.id
  }

  /**
   * Undo the last operation
   */
  async undoLastOperation(): Promise<boolean> {
    const lastOperation = this.undoStack.pop()
    if (!lastOperation || !lastOperation.canUndo) {
      return false
    }

    try {
      await this.executeUndoOperation(lastOperation)
      return true
    } catch (error) {
      console.error('Undo failed:', error)
      return false
    }
  }

  /**
   * Cancel an ongoing operation
   */
  cancelOperation(operationId: string): boolean {
    const operation = this.operations.get(operationId)
    if (!operation || operation.status !== 'running') {
      return false
    }

    operation.status = 'cancelled'
    operation.endTime = new Date()
    this.notifyListeners(operation)
    return true
  }

  /**
   * Get operation by ID
   */
  getOperation(operationId: string): FileOperation | undefined {
    return this.operations.get(operationId)
  }

  /**
   * Get all operations
   */
  getAllOperations(): FileOperation[] {
    return Array.from(this.operations.values())
  }

  /**
   * Get active operations
   */
  getActiveOperations(): FileOperation[] {
    return this.getAllOperations().filter(op => 
      op.status === 'pending' || op.status === 'running'
    )
  }

  /**
   * Get undo stack
   */
  getUndoStack(): FileOperation[] {
    return [...this.undoStack]
  }

  /**
   * Add operation listener
   */
  addOperationListener(id: string, callback: (operation: FileOperation) => void): void {
    this.listeners.set(id, callback)
  }

  /**
   * Remove operation listener
   */
  removeOperationListener(id: string): void {
    this.listeners.delete(id)
  }

  /**
   * Add progress listener
   */
  addProgressListener(id: string, callback: (progress: OperationProgress) => void): void {
    this.progressListeners.set(id, callback)
  }

  /**
   * Remove progress listener
   */
  removeProgressListener(id: string): void {
    this.progressListeners.delete(id)
  }

  /**
   * Clear completed operations
   */
  clearCompletedOperations(): void {
    const completedIds: string[] = []
    this.operations.forEach((operation, id) => {
      if (operation.status === 'completed' || operation.status === 'failed') {
        completedIds.push(id)
      }
    })
    
    completedIds.forEach(id => this.operations.delete(id))
  }

  /**
   * Create a new operation
   */
  private createOperation(
    type: OperationType, 
    sourceItems: FileSystemItem[], 
    targetPath?: string,
    targetName?: string
  ): FileOperation {
    const operation: FileOperation = {
      id: this.generateOperationId(),
      type,
      status: 'pending',
      sourceItems: sourceItems.map(item => ({ ...item })),
      targetPath,
      targetName,
      progress: 0,
      startTime: new Date(),
      canUndo: false
    }

    this.operations.set(operation.id, operation)
    this.notifyListeners(operation)
    return operation
  }

  /**
   * Execute an operation
   */
  private async executeOperation(
    operation: FileOperation, 
    executor: (op: FileOperation) => Promise<void>
  ): Promise<void> {
    operation.status = 'running'
    this.notifyListeners(operation)

    try {
      await executor(operation)
      operation.status = 'completed'
      operation.progress = 100
      operation.endTime = new Date()
      
      if (operation.canUndo) {
        this.undoStack.push(operation)
        if (this.undoStack.length > this.maxUndoOperations) {
          this.undoStack.shift()
        }
      }
    } catch (error) {
      operation.status = 'failed'
      operation.error = error instanceof Error ? error.message : 'Unknown error'
      operation.endTime = new Date()
      throw error
    } finally {
      this.notifyListeners(operation)
    }
  }

  /**
   * Execute undo operation
   */
  private async executeUndoOperation(operation: FileOperation): Promise<void> {
    const undoData = operation.undoData
    if (!undoData) return

    switch (undoData.type) {
      case 'copy':
        // Delete copied files
        for (const item of undoData.copiedItems) {
          await this.simulateFileOperation(`Deleting ${item.name}`, 500)
        }
        break
        
      case 'move':
        // Move files back to original locations
        for (const { item, originalPath } of undoData.originalLocations) {
          await this.simulateFileOperation(`Restoring ${item.name}`, 500)
        }
        break
        
      case 'delete':
        // Restore deleted files
        for (const item of undoData.deletedItems) {
          await this.simulateFileOperation(`Restoring ${item.name}`, 500)
        }
        break
        
      case 'rename':
        // Restore original name
        await this.simulateFileOperation(`Renaming ${undoData.newName} to ${undoData.originalName}`, 300)
        break
    }
  }

  /**
   * Simulate individual file operations
   */
  private async copyItem(
    item: FileSystemItem, 
    targetPath: string, 
    operationId: string, 
    processedFiles: number, 
    totalFiles: number
  ): Promise<void> {
    await this.simulateFileOperation(`Copying ${item.name}`, this.getOperationDuration(item))
    this.updateProgress(operationId, item.name, processedFiles + 1, totalFiles)
  }

  private async moveItem(
    item: FileSystemItem, 
    targetPath: string, 
    operationId: string, 
    processedFiles: number, 
    totalFiles: number
  ): Promise<void> {
    await this.simulateFileOperation(`Moving ${item.name}`, this.getOperationDuration(item))
    this.updateProgress(operationId, item.name, processedFiles + 1, totalFiles)
  }

  private async deleteItem(
    item: FileSystemItem, 
    operationId: string, 
    processedFiles: number, 
    totalFiles: number
  ): Promise<void> {
    await this.simulateFileOperation(`Deleting ${item.name}`, this.getOperationDuration(item) * 0.5)
    this.updateProgress(operationId, item.name, processedFiles + 1, totalFiles)
  }

  private async renameItem(
    item: FileSystemItem, 
    newName: string, 
    operationId: string
  ): Promise<void> {
    await this.simulateFileOperation(`Renaming ${item.name} to ${newName}`, 300)
    this.updateProgress(operationId, newName, 1, 1)
  }

  /**
   * Simulate file operation with realistic timing
   */
  private async simulateFileOperation(description: string, duration: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, duration))
  }

  /**
   * Get realistic operation duration based on file characteristics
   */
  private getOperationDuration(item: FileSystemItem): number {
    const baseTime = 200 // Base time in ms
    const nameLength = item.name.length
    const isFolder = item.type === 'folder'
    
    // Simulate realistic timing based on file type and size
    let duration = baseTime + (nameLength * 10)
    
    if (isFolder) {
      duration *= 2 // Folders take longer
    }
    
    // Add some randomness for realism
    duration += Math.random() * 200
    
    return Math.floor(duration)
  }

  /**
   * Count total files in items (including nested)
   */
  private countFiles(items: FileSystemItem[]): number {
    let count = 0
    for (const item of items) {
      count++
      if (item.files) {
        count += this.countFiles(item.files)
      }
    }
    return count
  }

  /**
   * Update operation progress
   */
  private updateProgress(
    operationId: string, 
    currentFile: string, 
    filesProcessed: number, 
    totalFiles: number
  ): void {
    const operation = this.operations.get(operationId)
    if (!operation) return

    operation.progress = Math.round((filesProcessed / totalFiles) * 100)
    
    const progress: OperationProgress = {
      operationId,
      currentFile,
      filesProcessed,
      totalFiles,
      bytesProcessed: filesProcessed * 1024, // Simulated
      totalBytes: totalFiles * 1024 // Simulated
    }

    this.notifyProgressListeners(progress)
    this.notifyListeners(operation)
  }

  /**
   * Fail an operation
   */
  private failOperation(operationId: string, error: string): void {
    const operation = this.operations.get(operationId)
    if (!operation) return

    operation.status = 'failed'
    operation.error = error
    operation.endTime = new Date()
    this.notifyListeners(operation)
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(): string {
    return `op-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Notify operation listeners
   */
  private notifyListeners(operation: FileOperation): void {
    this.listeners.forEach(callback => callback(operation))
  }

  /**
   * Notify progress listeners
   */
  private notifyProgressListeners(progress: OperationProgress): void {
    this.progressListeners.forEach(callback => callback(progress))
  }
}
