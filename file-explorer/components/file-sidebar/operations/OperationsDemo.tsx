/**
 * File Operations Demo Component
 * Demonstrates file operations feedback with interactive examples
 */

import React, { useState } from 'react'
import { Play, Copy, Move, Trash2, Edit, Plus, Undo, BarChart3 } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { cn } from "@/lib/utils"
import { FileSystemItem } from '../types'
import { useFileOperations, useOperationUI } from './useFileOperations'
import { OperationsFeedback, OperationProgressToast, UndoButton } from './OperationsFeedback'

// Sample files for demonstration
const sampleFiles: FileSystemItem[] = [
  { id: 'file1', name: 'App.tsx', type: 'tsx', path: '/project/src/App.tsx' },
  { id: 'file2', name: 'index.ts', type: 'ts', path: '/project/src/index.ts' },
  { id: 'file3', name: 'styles.css', type: 'css', path: '/project/src/styles.css' },
  { id: 'file4', name: 'README.md', type: 'md', path: '/project/README.md' },
  { id: 'file5', name: 'package.json', type: 'json', path: '/project/package.json' },
  { id: 'file6', name: 'config.yaml', type: 'yaml', path: '/project/config.yaml' },
  { id: 'file7', name: 'logo.png', type: 'png', path: '/project/assets/logo.png' },
  { id: 'file8', name: 'utils.js', type: 'js', path: '/project/src/utils.js' },
  { id: 'file9', name: 'test.spec.ts', type: 'ts', path: '/project/tests/test.spec.ts' },
  { id: 'file10', name: 'database.sql', type: 'sql', path: '/project/db/database.sql' }
]

export const OperationsDemo: React.FC = () => {
  const [selectedFiles, setSelectedFiles] = useState<FileSystemItem[]>([])
  const [targetPath, setTargetPath] = useState('/project/backup')
  const [newName, setNewName] = useState('')
  const [showToast, setShowToast] = useState(false)
  const [toastOperation, setToastOperation] = useState<any>(null)

  const fileOps = useFileOperations({
    autoCleanup: false, // Keep operations for demo
    maxDisplayOperations: 20,
    onOperationComplete: (operation) => {
      console.log('Operation completed:', operation)
      setToastOperation(operation)
      setShowToast(true)
      setTimeout(() => setShowToast(false), 3000)
    },
    onOperationFailed: (operation) => {
      console.error('Operation failed:', operation)
      setToastOperation(operation)
      setShowToast(true)
      setTimeout(() => setShowToast(false), 5000)
    }
  })

  const uiHelpers = useOperationUI()

  const handleFileSelection = (file: FileSystemItem) => {
    setSelectedFiles(prev => {
      const isSelected = prev.some(f => f.id === file.id)
      if (isSelected) {
        return prev.filter(f => f.id !== file.id)
      } else {
        return [...prev, file]
      }
    })
  }

  const simulateCopy = async () => {
    if (selectedFiles.length === 0) return
    try {
      await fileOps.copyFiles(selectedFiles, targetPath)
    } catch (error) {
      console.error('Copy failed:', error)
    }
  }

  const simulateMove = async () => {
    if (selectedFiles.length === 0) return
    try {
      await fileOps.moveFiles(selectedFiles, targetPath)
    } catch (error) {
      console.error('Move failed:', error)
    }
  }

  const simulateDelete = async () => {
    if (selectedFiles.length === 0) return
    try {
      await fileOps.deleteFiles(selectedFiles)
    } catch (error) {
      console.error('Delete failed:', error)
    }
  }

  const simulateRename = async () => {
    if (selectedFiles.length !== 1 || !newName) return
    try {
      await fileOps.renameFile(selectedFiles[0], newName)
    } catch (error) {
      console.error('Rename failed:', error)
    }
  }

  const runBatchOperations = async () => {
    const operations = [
      () => fileOps.copyFiles([sampleFiles[0], sampleFiles[1]], '/project/backup'),
      () => fileOps.moveFiles([sampleFiles[2]], '/project/archive'),
      () => fileOps.renameFile(sampleFiles[3], 'DOCUMENTATION.md'),
      () => fileOps.deleteFiles([sampleFiles[4]]),
      () => fileOps.copyFiles([sampleFiles[5], sampleFiles[6]], '/project/temp')
    ]

    for (let i = 0; i < operations.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      try {
        await operations[i]()
      } catch (error) {
        console.error(`Batch operation ${i + 1} failed:`, error)
      }
    }
  }

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Play className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-bold">File Operations Demo</h2>
        </div>
        <p className="text-muted-foreground">
          Explore file operations with real-time feedback and undo functionality
        </p>
      </div>

      {/* File Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Files for Operations</CardTitle>
          <CardDescription>
            Click files to select them for operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
            {sampleFiles.map((file) => (
              <div
                key={file.id}
                className={cn(
                  "flex items-center gap-2 p-2 border border-border rounded cursor-pointer transition-colors",
                  selectedFiles.some(f => f.id === file.id) 
                    ? "bg-primary/10 border-primary" 
                    : "hover:bg-accent/50"
                )}
                onClick={() => handleFileSelection(file)}
              >
                <span>{getFileIcon(file.name)}</span>
                <span className="text-sm font-medium truncate">{file.name}</span>
              </div>
            ))}
          </div>
          
          {selectedFiles.length > 0 && (
            <div className="mt-3 flex items-center gap-2">
              <Badge variant="secondary">
                {selectedFiles.length} file{selectedFiles.length !== 1 ? 's' : ''} selected
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedFiles([])}
              >
                Clear Selection
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Operation Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="w-4 h-4" />
            File Operations
          </CardTitle>
          <CardDescription>
            Perform operations on selected files
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Target Path */}
          <div>
            <Label htmlFor="target-path" className="text-sm font-medium">
              Target Path
            </Label>
            <Input
              id="target-path"
              value={targetPath}
              onChange={(e) => setTargetPath(e.target.value)}
              placeholder="/project/backup"
              className="mt-1"
            />
          </div>

          {/* Rename Input */}
          {selectedFiles.length === 1 && (
            <div>
              <Label htmlFor="new-name" className="text-sm font-medium">
                New Name (for rename)
              </Label>
              <Input
                id="new-name"
                value={newName}
                onChange={(e) => setNewName(e.target.value)}
                placeholder="Enter new name"
                className="mt-1"
              />
            </div>
          )}

          {/* Operation Buttons */}
          <div className="flex items-center gap-2 flex-wrap">
            <Button
              onClick={simulateCopy}
              disabled={selectedFiles.length === 0}
              className="gap-1"
            >
              <Copy className="w-3 h-3" />
              Copy
            </Button>
            
            <Button
              onClick={simulateMove}
              disabled={selectedFiles.length === 0}
              variant="outline"
              className="gap-1"
            >
              <Move className="w-3 h-3" />
              Move
            </Button>
            
            <Button
              onClick={simulateDelete}
              disabled={selectedFiles.length === 0}
              variant="outline"
              className="gap-1"
            >
              <Trash2 className="w-3 h-3" />
              Delete
            </Button>
            
            <Button
              onClick={simulateRename}
              disabled={selectedFiles.length !== 1 || !newName}
              variant="outline"
              className="gap-1"
            >
              <Edit className="w-3 h-3" />
              Rename
            </Button>
          </div>

          {/* Batch Operations */}
          <div className="pt-2 border-t">
            <Button
              onClick={runBatchOperations}
              variant="secondary"
              className="gap-1"
            >
              <Play className="w-3 h-3" />
              Run Batch Operations
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Operations Feedback */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Full Operations Display */}
        <Card>
          <CardHeader>
            <CardTitle>Operations Feedback</CardTitle>
            <CardDescription>
              Real-time progress and status updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <OperationsFeedback
              compact={false}
              showCompleted={true}
              maxItems={8}
            />
          </CardContent>
        </Card>

        {/* Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Total Operations</span>
                <Badge variant="outline">{fileOps.operations.length}</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Active Operations</span>
                <Badge variant="secondary">{fileOps.activeOperations.length}</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Undo Available</span>
                <Badge variant={fileOps.canUndo ? "default" : "outline"}>
                  {fileOps.canUndo ? "Yes" : "No"}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Undo Stack</span>
                <Badge variant="outline">{fileOps.undoStack.length}</Badge>
              </div>
            </div>

            {/* Undo Button */}
            <div className="mt-4 pt-3 border-t">
              <UndoButton
                canUndo={fileOps.canUndo}
                onUndo={fileOps.undoLastOperation}
                className="w-full"
              />
            </div>

            {/* Clear Operations */}
            <Button
              variant="outline"
              size="sm"
              onClick={fileOps.clearCompletedOperations}
              className="w-full mt-2"
            >
              Clear Completed
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* How It Works */}
      <Card>
        <CardHeader>
          <CardTitle>How File Operations Work</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">🔄 Real-time Progress</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Operations show live progress with file-by-file updates
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Progress bars for long operations</li>
                <li>• Current file being processed</li>
                <li>• Files processed vs total count</li>
                <li>• Estimated time remaining</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">↩️ Undo Functionality</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Most operations can be undone with full state restoration
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Copy: Removes copied files</li>
                <li>• Move: Restores original locations</li>
                <li>• Delete: Restores deleted files</li>
                <li>• Rename: Restores original names</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">⚡ Operation Types</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Support for all common file operations
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• 📋 Copy: Duplicate files to new location</li>
                <li>• 🔄 Move: Transfer files to new location</li>
                <li>• 🗑️ Delete: Remove files permanently</li>
                <li>• ✏️ Rename: Change file names</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">🎯 Error Handling</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Robust error handling with detailed feedback
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Clear error messages</li>
                <li>• Operation cancellation</li>
                <li>• Partial completion handling</li>
                <li>• Retry mechanisms</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Toast Notification */}
      {showToast && toastOperation && (
        <OperationProgressToast
          operation={toastOperation}
          progress={fileOps.getOperationProgress(toastOperation.id)}
          onDismiss={() => setShowToast(false)}
          className="fixed top-4 right-4 z-50"
        />
      )}
    </div>
  )
}

/**
 * Get file icon based on extension
 */
function getFileIcon(filename: string): string {
  const extension = filename.split('.').pop()?.toLowerCase() || ''
  
  const icons: Record<string, string> = {
    'js': '🟨', 'jsx': '⚛️', 'ts': '🔷', 'tsx': '⚛️',
    'html': '🌐', 'css': '🎨', 'scss': '🎨', 'json': '📋',
    'md': '📝', 'txt': '📄', 'png': '🖼️', 'jpg': '🖼️',
    'pdf': '📕', 'zip': '📦', 'mp4': '🎬', 'mp3': '🎵',
    'yaml': '⚙️', 'sql': '🗄️'
  }
  
  return icons[extension] || '📄'
}

export default OperationsDemo
