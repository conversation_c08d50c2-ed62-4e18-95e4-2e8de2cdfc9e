/**
 * File Operations Hook
 * React hook for integrating file operations with components
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import { FileSystemItem } from '../types'
import { FileOperationsManager, FileOperation, OperationProgress, OperationType } from './FileOperationsManager'

export interface FileOperationsOptions {
  autoCleanup?: boolean
  maxDisplayOperations?: number
  onOperationComplete?: (operation: FileOperation) => void
  onOperationFailed?: (operation: FileOperation) => void
}

export interface FileOperationsResult {
  operations: FileOperation[]
  activeOperations: FileOperation[]
  undoStack: FileOperation[]
  copyFiles: (sourceItems: FileSystemItem[], targetPath: string) => Promise<string>
  moveFiles: (sourceItems: FileSystemItem[], targetPath: string) => Promise<string>
  deleteFiles: (sourceItems: FileSystemItem[]) => Promise<string>
  renameFile: (sourceItem: FileSystemItem, newName: string) => Promise<string>
  undoLastOperation: () => Promise<boolean>
  cancelOperation: (operationId: string) => boolean
  clearCompletedOperations: () => void
  getOperationProgress: (operationId: string) => OperationProgress | null
  isOperationRunning: (operationId: string) => boolean
  canUndo: boolean
}

export const useFileOperations = (options: FileOperationsOptions = {}): FileOperationsResult => {
  const {
    autoCleanup = true,
    maxDisplayOperations = 10,
    onOperationComplete,
    onOperationFailed
  } = options

  const [operations, setOperations] = useState<FileOperation[]>([])
  const [progressMap, setProgressMap] = useState<Map<string, OperationProgress>>(new Map())
  const [manager] = useState(() => FileOperationsManager.getInstance())

  // Setup listeners
  useEffect(() => {
    const operationListenerId = 'file-operations-hook'
    const progressListenerId = 'file-operations-progress'

    const handleOperationUpdate = (operation: FileOperation) => {
      setOperations(prev => {
        const updated = prev.filter(op => op.id !== operation.id)
        updated.push(operation)
        
        // Limit displayed operations
        if (updated.length > maxDisplayOperations) {
          updated.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
          return updated.slice(0, maxDisplayOperations)
        }
        
        return updated.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      })

      // Handle completion callbacks
      if (operation.status === 'completed') {
        onOperationComplete?.(operation)
      } else if (operation.status === 'failed') {
        onOperationFailed?.(operation)
      }
    }

    const handleProgressUpdate = (progress: OperationProgress) => {
      setProgressMap(prev => new Map(prev.set(progress.operationId, progress)))
    }

    manager.addOperationListener(operationListenerId, handleOperationUpdate)
    manager.addProgressListener(progressListenerId, handleProgressUpdate)

    // Load existing operations
    setOperations(manager.getAllOperations().slice(0, maxDisplayOperations))

    return () => {
      manager.removeOperationListener(operationListenerId)
      manager.removeProgressListener(progressListenerId)
    }
  }, [manager, maxDisplayOperations, onOperationComplete, onOperationFailed])

  // Auto cleanup completed operations
  useEffect(() => {
    if (!autoCleanup) return

    const interval = setInterval(() => {
      const now = new Date()
      const cutoff = new Date(now.getTime() - 5 * 60 * 1000) // 5 minutes ago

      setOperations(prev => prev.filter(op => {
        if (op.status === 'completed' || op.status === 'failed') {
          return op.endTime ? op.endTime > cutoff : true
        }
        return true
      }))
    }, 60 * 1000) // Check every minute

    return () => clearInterval(interval)
  }, [autoCleanup])

  // Get active operations
  const activeOperations = useMemo(() => {
    return operations.filter(op => op.status === 'pending' || op.status === 'running')
  }, [operations])

  // Get undo stack
  const undoStack = useMemo(() => {
    return manager.getUndoStack()
  }, [manager, operations]) // Re-compute when operations change

  // Check if undo is available
  const canUndo = useMemo(() => {
    return undoStack.length > 0 && undoStack[undoStack.length - 1]?.canUndo
  }, [undoStack])

  /**
   * Copy files
   */
  const copyFiles = useCallback(async (sourceItems: FileSystemItem[], targetPath: string): Promise<string> => {
    try {
      return await manager.copyFiles(sourceItems, targetPath)
    } catch (error) {
      console.error('Copy operation failed:', error)
      throw error
    }
  }, [manager])

  /**
   * Move files
   */
  const moveFiles = useCallback(async (sourceItems: FileSystemItem[], targetPath: string): Promise<string> => {
    try {
      return await manager.moveFiles(sourceItems, targetPath)
    } catch (error) {
      console.error('Move operation failed:', error)
      throw error
    }
  }, [manager])

  /**
   * Delete files
   */
  const deleteFiles = useCallback(async (sourceItems: FileSystemItem[]): Promise<string> => {
    try {
      return await manager.deleteFiles(sourceItems)
    } catch (error) {
      console.error('Delete operation failed:', error)
      throw error
    }
  }, [manager])

  /**
   * Rename file
   */
  const renameFile = useCallback(async (sourceItem: FileSystemItem, newName: string): Promise<string> => {
    try {
      return await manager.renameFile(sourceItem, newName)
    } catch (error) {
      console.error('Rename operation failed:', error)
      throw error
    }
  }, [manager])

  /**
   * Undo last operation
   */
  const undoLastOperation = useCallback(async (): Promise<boolean> => {
    try {
      return await manager.undoLastOperation()
    } catch (error) {
      console.error('Undo operation failed:', error)
      return false
    }
  }, [manager])

  /**
   * Cancel operation
   */
  const cancelOperation = useCallback((operationId: string): boolean => {
    return manager.cancelOperation(operationId)
  }, [manager])

  /**
   * Clear completed operations
   */
  const clearCompletedOperations = useCallback(() => {
    manager.clearCompletedOperations()
    setOperations(manager.getAllOperations().slice(0, maxDisplayOperations))
  }, [manager, maxDisplayOperations])

  /**
   * Get operation progress
   */
  const getOperationProgress = useCallback((operationId: string): OperationProgress | null => {
    return progressMap.get(operationId) || null
  }, [progressMap])

  /**
   * Check if operation is running
   */
  const isOperationRunning = useCallback((operationId: string): boolean => {
    const operation = manager.getOperation(operationId)
    return operation?.status === 'running' || false
  }, [manager])

  return {
    operations,
    activeOperations,
    undoStack,
    copyFiles,
    moveFiles,
    deleteFiles,
    renameFile,
    undoLastOperation,
    cancelOperation,
    clearCompletedOperations,
    getOperationProgress,
    isOperationRunning,
    canUndo
  }
}

/**
 * Hook for operation UI helpers
 */
export const useOperationUI = () => {
  /**
   * Get operation icon
   */
  const getOperationIcon = useCallback((type: OperationType): string => {
    const icons: Record<OperationType, string> = {
      copy: '📋',
      move: '🔄',
      delete: '🗑️',
      rename: '✏️',
      create: '✨',
      paste: '📋'
    }
    return icons[type] || '📄'
  }, [])

  /**
   * Get operation color
   */
  const getOperationColor = useCallback((type: OperationType): string => {
    const colors: Record<OperationType, string> = {
      copy: 'text-blue-600 dark:text-blue-400',
      move: 'text-orange-600 dark:text-orange-400',
      delete: 'text-red-600 dark:text-red-400',
      rename: 'text-purple-600 dark:text-purple-400',
      create: 'text-green-600 dark:text-green-400',
      paste: 'text-blue-600 dark:text-blue-400'
    }
    return colors[type] || 'text-gray-600 dark:text-gray-400'
  }, [])

  /**
   * Get status icon
   */
  const getStatusIcon = useCallback((status: string): string => {
    const icons: Record<string, string> = {
      pending: '⏳',
      running: '⚡',
      completed: '✅',
      failed: '❌',
      cancelled: '⏹️'
    }
    return icons[status] || '❓'
  }, [])

  /**
   * Get status color
   */
  const getStatusColor = useCallback((status: string): string => {
    const colors: Record<string, string> = {
      pending: 'text-yellow-600 dark:text-yellow-400',
      running: 'text-blue-600 dark:text-blue-400',
      completed: 'text-green-600 dark:text-green-400',
      failed: 'text-red-600 dark:text-red-400',
      cancelled: 'text-gray-600 dark:text-gray-400'
    }
    return colors[status] || 'text-gray-600 dark:text-gray-400'
  }, [])

  /**
   * Format operation duration
   */
  const formatDuration = useCallback((startTime: Date, endTime?: Date): string => {
    const end = endTime || new Date()
    const diffMs = end.getTime() - startTime.getTime()
    const diffSeconds = Math.floor(diffMs / 1000)
    
    if (diffSeconds < 60) {
      return `${diffSeconds}s`
    }
    
    const diffMinutes = Math.floor(diffSeconds / 60)
    const remainingSeconds = diffSeconds % 60
    
    return `${diffMinutes}m ${remainingSeconds}s`
  }, [])

  /**
   * Format file count
   */
  const formatFileCount = useCallback((count: number): string => {
    if (count === 1) return '1 file'
    return `${count} files`
  }, [])

  /**
   * Format bytes
   */
  const formatBytes = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
  }, [])

  return {
    getOperationIcon,
    getOperationColor,
    getStatusIcon,
    getStatusColor,
    formatDuration,
    formatFileCount,
    formatBytes
  }
}

/**
 * Hook for operation notifications
 */
export const useOperationNotifications = () => {
  const [notifications, setNotifications] = useState<Array<{
    id: string
    operation: FileOperation
    timestamp: Date
  }>>([])

  const addNotification = useCallback((operation: FileOperation) => {
    const notification = {
      id: `notif-${operation.id}`,
      operation,
      timestamp: new Date()
    }
    
    setNotifications(prev => [notification, ...prev.slice(0, 4)]) // Keep last 5
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id))
    }, 5000)
  }, [])

  const removeNotification = useCallback((notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId))
  }, [])

  const clearNotifications = useCallback(() => {
    setNotifications([])
  }, [])

  return {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications
  }
}
