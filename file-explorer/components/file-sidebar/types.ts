/**
 * File Sidebar Types
 * Shared type definitions for the file sidebar components
 */

// FileSystemItem type definition
export interface FileSystemItem {
  id: number | string
  name: string
  type: string
  path?: string
  content?: string
  size?: number
  modified?: Date
  expanded?: boolean
  files?: FileSystemItem[]
}

// Props for the main FileSidebar component
export interface FileSidebarProps {
  onFileSelect: (file: FileSystemItem) => void
  onCreateProject?: () => void
  onRefreshRequest?: () => void
  onProjectsChange?: (projects: FileSystemItem[]) => void
}

// Props for FileTreeItem component
export interface FileTreeItemProps {
  item: FileSystemItem
  level?: number
  onToggle: (id: number | string) => void
  onSelect: (file: FileSystemItem) => void
  selectedFile: FileSystemItem | null
}

// Props for SidebarItem component
export interface SidebarItemProps {
  item: FileSystemItem
  level?: number
  onToggle: (id: number | string) => void
  onSelect: (file: FileSystemItem) => void
  selectedFile: FileSystemItem | null
  showFileIcons?: boolean
}

// Props for ProjectSection component
export interface ProjectSectionProps {
  projects: FileSystemItem[]
  selectedFile: FileSystemItem | null
  onToggle: (id: number | string) => void
  onSelect: (file: FileSystemItem) => void
  onCreateProject: () => void
  onOpenProject: () => void
}

// Props for SidebarLayout component
export interface SidebarLayoutProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  onCreateProject: () => void
  onOpenProject: () => void
  onStartOrchestration: () => void
  onShowSettings: () => void
  onFileSelect: (file: FileSystemItem) => void
  projectCount: number
  hasProjects: boolean
  projects: FileSystemItem[]
  children: React.ReactNode
}

// Event handler types
export interface FileSidebarEvents {
  onFileSelect: (file: FileSystemItem) => void
  onToggleFolder: (id: number | string) => Promise<void>
  onCreateProject: () => void
  onOpenProject: () => Promise<void>
  onStartOrchestration: () => void
  onShowSettings: () => void
  onSearchChange: (query: string) => void
  onSwitchProject: (name: string, path: string) => Promise<void>
}

// PRD related types
export interface PRDValidationResult {
  isValid: boolean
  score: number
  errors?: string[]
}

export interface PRDParseResult {
  success: boolean
  taskCount?: number
  error?: string
}

// Recent project type
export interface RecentProject {
  name: string
  path: string
  lastOpened: number
}

// Explorer settings type
export interface ExplorerSettings {
  showHiddenFiles: boolean
  autoExpandFolders: boolean
  showFileExtensions: boolean
  compactView: boolean
  showFileIcons: boolean
  sortBy: 'name' | 'modified' | 'size' | 'type'
  sortOrder: 'asc' | 'desc'
}

// Dialog props
export interface UnsavedChangesDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (saveChanges: boolean) => void
}

export interface PRDDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentProjectPath: string | null
  prdValidated: boolean
  onPRDUploaded: (filePath: string, validation: PRDValidationResult) => void
  onPRDParsed: (result: PRDParseResult) => void
  onValidationChange: (isValid: boolean) => void
  onCancel: () => void
}

export interface OrchestrationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onTasksSubmitted: (result: { success: boolean; tasksSubmitted: number; error?: string }) => void
}

export interface ExplorerSettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  settings: ExplorerSettings
  onSettingsChange: (settings: Partial<ExplorerSettings>) => void
  onSave: () => void
}
