/**
 * Drag and Drop Manager
 * Handles drag and drop operations for file explorer
 */

import { FileSystemItem } from '../types'

export interface DragData {
  type: 'file' | 'folder'
  item: FileSystemItem
  sourceIndex?: number
  sourceParent?: FileSystemItem
}

export interface DropTarget {
  item: FileSystemItem
  position: 'before' | 'after' | 'inside'
  targetIndex?: number
}

export interface DragDropResult {
  success: boolean
  operation: 'move' | 'copy' | 'reorder'
  source: FileSystemItem
  target: FileSystemItem
  error?: string
}

export type DragDropOperation = 'move' | 'copy' | 'reorder'

export class DragDropManager {
  private dragData: DragData | null = null
  private dropTarget: DropTarget | null = null
  private dragOverElement: HTMLElement | null = null
  private dragPreview: HTMLElement | null = null

  /**
   * Start drag operation
   */
  startDrag(item: FileSystemItem, sourceElement: HTMLElement, event: DragEvent): void {
    this.dragData = {
      type: item.type === 'folder' ? 'folder' : 'file',
      item,
      sourceIndex: this.getElementIndex(sourceElement),
      sourceParent: this.getParentItem(sourceElement)
    }

    // Set drag effect
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'copyMove'
      event.dataTransfer.setData('text/plain', item.name)
      
      // Create custom drag preview
      this.createDragPreview(item, event)
    }

    // Add visual feedback to source element
    sourceElement.classList.add('dragging')
    
    console.log('Drag started:', this.dragData)
  }

  /**
   * Handle drag over event
   */
  handleDragOver(targetElement: HTMLElement, event: DragEvent): boolean {
    event.preventDefault()
    
    if (!this.dragData) return false

    const targetItem = this.getItemFromElement(targetElement)
    if (!targetItem) return false

    // Determine drop position
    const position = this.getDropPosition(targetElement, event)
    
    // Check if drop is valid
    if (!this.isValidDrop(this.dragData.item, targetItem, position)) {
      event.dataTransfer!.dropEffect = 'none'
      this.clearDropTarget()
      return false
    }

    // Set drop effect based on modifier keys
    const operation = this.getDropOperation(event)
    event.dataTransfer!.dropEffect = operation === 'copy' ? 'copy' : 'move'

    // Update drop target
    this.updateDropTarget(targetItem, position, targetElement)
    
    return true
  }

  /**
   * Handle drop event
   */
  async handleDrop(targetElement: HTMLElement, event: DragEvent): Promise<DragDropResult | null> {
    event.preventDefault()
    
    if (!this.dragData || !this.dropTarget) {
      this.cleanup()
      return null
    }

    const operation = this.getDropOperation(event)
    const result = await this.executeDrop(operation)
    
    this.cleanup()
    return result
  }

  /**
   * Handle drag end event
   */
  handleDragEnd(sourceElement: HTMLElement): void {
    sourceElement.classList.remove('dragging')
    this.cleanup()
  }

  /**
   * Check if drop is valid
   */
  private isValidDrop(source: FileSystemItem, target: FileSystemItem, position: 'before' | 'after' | 'inside'): boolean {
    // Can't drop on itself
    if (source.id === target.id) return false

    // Can't drop parent into child
    if (this.isParentOf(source, target)) return false

    // Can only drop inside folders
    if (position === 'inside' && target.type !== 'folder') return false

    // Can't drop folder into file
    if (source.type === 'folder' && target.type !== 'folder' && position === 'inside') return false

    return true
  }

  /**
   * Get drop operation based on modifier keys
   */
  private getDropOperation(event: DragEvent): DragDropOperation {
    if (event.ctrlKey || event.metaKey) return 'copy'
    if (event.altKey) return 'reorder'
    return 'move'
  }

  /**
   * Execute the drop operation
   */
  private async executeDrop(operation: DragDropOperation): Promise<DragDropResult> {
    if (!this.dragData || !this.dropTarget) {
      return {
        success: false,
        operation,
        source: this.dragData!.item,
        target: this.dropTarget!.item,
        error: 'Invalid drag/drop state'
      }
    }

    try {
      const source = this.dragData.item
      const target = this.dropTarget.item

      let targetPath: string

      if (this.dropTarget.position === 'inside') {
        // Drop inside folder
        targetPath = `${target.path}/${source.name}`
      } else {
        // Drop before/after - same parent directory
        const parentPath = this.getParentPath(target.path || '')
        targetPath = `${parentPath}/${source.name}`
      }

      let result

      switch (operation) {
        case 'copy':
          // For now, simulate copy operation
          console.log(`Copying ${source.name} to ${targetPath}`)
          result = { success: true }
          break

        case 'move':
          // For now, simulate move operation
          console.log(`Moving ${source.name} to ${targetPath}`)
          result = { success: true }
          break

        case 'reorder':
          // For reordering, we just need to update the UI state
          console.log(`Reordering ${source.name}`)
          result = { success: true }
          break

        default:
          throw new Error(`Unsupported operation: ${operation}`)
      }

      if (result.success) {
        // Trigger file explorer refresh
        await this.triggerFileExplorerRefresh()
      }

      return {
        success: result.success,
        operation,
        source,
        target,
        error: result.error
      }

    } catch (error) {
      return {
        success: false,
        operation,
        source: this.dragData.item,
        target: this.dropTarget.item,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * Copy folder recursively
   */
  private async copyFolderRecursively(sourcePath: string, targetPath: string): Promise<{ success: boolean; error?: string }> {
    try {
      // This would need to be implemented based on the available APIs
      // For now, return success for folders
      console.log(`Copying folder from ${sourcePath} to ${targetPath}`)
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * Create drag preview element
   */
  private createDragPreview(item: FileSystemItem, event: DragEvent): void {
    const preview = document.createElement('div')
    preview.className = 'drag-preview'
    preview.textContent = item.name
    preview.style.position = 'absolute'
    preview.style.top = '-1000px'
    preview.style.pointerEvents = 'none'
    
    document.body.appendChild(preview)
    this.dragPreview = preview

    if (event.dataTransfer) {
      event.dataTransfer.setDragImage(preview, 0, 0)
    }
  }

  /**
   * Update drop target visual feedback
   */
  private updateDropTarget(item: FileSystemItem, position: 'before' | 'after' | 'inside', element: HTMLElement): void {
    // Clear previous drop target
    this.clearDropTarget()

    this.dropTarget = { item, position }
    this.dragOverElement = element

    // Add visual feedback
    element.classList.add('drop-target')
    element.classList.add(`drop-${position}`)
  }

  /**
   * Clear drop target visual feedback
   */
  private clearDropTarget(): void {
    if (this.dragOverElement) {
      this.dragOverElement.classList.remove('drop-target', 'drop-before', 'drop-after', 'drop-inside')
      this.dragOverElement = null
    }
    this.dropTarget = null
  }

  /**
   * Cleanup drag operation
   */
  private cleanup(): void {
    this.clearDropTarget()
    this.dragData = null

    if (this.dragPreview) {
      document.body.removeChild(this.dragPreview)
      this.dragPreview = null
    }
  }

  /**
   * Get drop position based on mouse position
   */
  private getDropPosition(element: HTMLElement, event: DragEvent): 'before' | 'after' | 'inside' {
    const rect = element.getBoundingClientRect()
    const y = event.clientY - rect.top
    const height = rect.height

    // If it's a folder, prefer 'inside' for middle area
    const item = this.getItemFromElement(element)
    if (item?.type === 'folder') {
      if (y < height * 0.25) return 'before'
      if (y > height * 0.75) return 'after'
      return 'inside'
    }

    // For files, only before/after
    return y < height / 2 ? 'before' : 'after'
  }

  /**
   * Helper methods
   */
  private getItemFromElement(element: HTMLElement): FileSystemItem | null {
    const itemId = element.dataset.itemId
    const itemPath = element.dataset.itemPath
    const itemType = element.dataset.itemType
    const itemName = element.dataset.itemName

    if (!itemId || !itemName || !itemType) return null

    return {
      id: itemId,
      name: itemName,
      type: itemType,
      path: itemPath || undefined
    }
  }

  private getElementIndex(element: HTMLElement): number {
    const parent = element.parentElement
    if (!parent) return -1
    return Array.from(parent.children).indexOf(element)
  }

  private getParentItem(element: HTMLElement): FileSystemItem | null {
    let parent = element.parentElement
    while (parent) {
      if (parent.dataset.itemId && parent.dataset.itemType === 'folder') {
        return this.getItemFromElement(parent)
      }
      parent = parent.parentElement
    }
    return null
  }

  private isParentOf(parent: FileSystemItem, child: FileSystemItem): boolean {
    // Check if parent is an ancestor of child
    if (!child.path || !parent.path) return false
    return child.path.startsWith(parent.path + '/')
  }

  private getParentPath(path: string): string {
    const lastSlash = path.lastIndexOf('/')
    return lastSlash > 0 ? path.substring(0, lastSlash) : ''
  }

  private async triggerFileExplorerRefresh(): Promise<void> {
    if (typeof window !== 'undefined' && window.refreshFileExplorer) {
      await window.refreshFileExplorer()
    }
  }
}
