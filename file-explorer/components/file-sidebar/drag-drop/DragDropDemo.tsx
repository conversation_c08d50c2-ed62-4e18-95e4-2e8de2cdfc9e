/**
 * <PERSON>ag and Drop Demo Component
 * Simple demo to test drag and drop functionality
 */

import React from 'react'
import { FileSystemItem } from '../types'
import { useDragDrop } from './useDragDrop'

const sampleFiles: FileSystemItem[] = [
  {
    id: 'file1',
    name: 'component.tsx',
    type: 'tsx',
    path: '/src/component.tsx'
  },
  {
    id: 'file2', 
    name: 'styles.css',
    type: 'css',
    path: '/src/styles.css'
  },
  {
    id: 'folder1',
    name: 'utils',
    type: 'folder',
    path: '/src/utils',
    files: [
      {
        id: 'file3',
        name: 'helpers.ts',
        type: 'ts',
        path: '/src/utils/helpers.ts'
      }
    ]
  }
]

export const DragDropDemo: React.FC = () => {
  const dragDrop = useDragDrop({
    onFileMove: (source, target) => {
      console.log('File moved:', source.name, 'to', target.name)
    },
    onFileCopy: (source, target) => {
      console.log('File copied:', source.name, 'to', target.name)
    },
    onFileReorder: (item, newIndex) => {
      console.log('File reordered:', item.name, 'to index', newIndex)
    }
  })

  return (
    <div className="p-4 space-y-2">
      <h3 className="text-lg font-semibold mb-4">Drag & Drop Demo</h3>
      <p className="text-sm text-muted-foreground mb-4">
        Try dragging files between items. Hold Ctrl to copy, Alt to reorder.
      </p>
      
      {sampleFiles.map((item) => (
        <DemoItem
          key={item.id}
          item={item}
          dragDrop={dragDrop}
        />
      ))}
    </div>
  )
}

interface DemoItemProps {
  item: FileSystemItem
  dragDrop: ReturnType<typeof useDragDrop>
}

const DemoItem: React.FC<DemoItemProps> = ({ item, dragDrop }) => {
  const isFolder = item.type === 'folder'

  return (
    <div
      className={`
        p-3 border rounded-md cursor-pointer transition-all duration-150
        ${dragDrop.isDragging && dragDrop.draggedItem?.id === item.id 
          ? 'opacity-50 scale-95' 
          : 'hover:bg-accent/20'
        }
      `}
      draggable
      data-item-id={item.id}
      data-item-path={item.path}
      data-item-type={item.type}
      data-item-name={item.name}
      onDragStart={(e) => {
        const element = e.currentTarget as HTMLElement
        dragDrop.onDragStart(item, element)(e.nativeEvent as DragEvent)
      }}
      onDragOver={(e) => {
        const element = e.currentTarget as HTMLElement
        dragDrop.onDragOver(element)(e.nativeEvent as DragEvent)
      }}
      onDrop={(e) => {
        const element = e.currentTarget as HTMLElement
        dragDrop.onDrop(element)(e.nativeEvent as DragEvent)
      }}
      onDragEnd={(e) => {
        const element = e.currentTarget as HTMLElement
        dragDrop.onDragEnd(element)(e.nativeEvent as DragEvent)
      }}
      onDragEnter={(e) => {
        const element = e.currentTarget as HTMLElement
        dragDrop.onDragEnter(element)(e.nativeEvent as DragEvent)
      }}
      onDragLeave={(e) => {
        const element = e.currentTarget as HTMLElement
        dragDrop.onDragLeave(element)(e.nativeEvent as DragEvent)
      }}
    >
      <div className="flex items-center gap-2">
        <span className="text-lg">
          {isFolder ? '📁' : '📄'}
        </span>
        <span className="font-medium">{item.name}</span>
        <span className="text-xs text-muted-foreground">
          ({item.type})
        </span>
      </div>
      
      {isFolder && item.files && (
        <div className="ml-6 mt-2 space-y-1">
          {item.files.map((file) => (
            <DemoItem
              key={file.id}
              item={file}
              dragDrop={dragDrop}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default DragDropDemo
