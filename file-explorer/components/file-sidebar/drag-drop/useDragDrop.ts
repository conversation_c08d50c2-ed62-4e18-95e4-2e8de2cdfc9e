/**
 * Drag and Drop Hook
 * React hook for managing drag and drop operations in file explorer
 */

import { useCallback, useRef, useState } from 'react'
import { FileSystemItem } from '../types'
import { DragDropManager, DragDropResult } from './DragDropManager'
import { useToast } from '@/components/ui/use-toast'

export interface DragDropHandlers {
  onDragStart: (item: FileSystemItem, element: HTMLElement) => (event: DragEvent) => void
  onDragOver: (element: HTMLElement) => (event: DragEvent) => void
  onDrop: (element: HTMLElement) => (event: DragEvent) => Promise<void>
  onDragEnd: (element: HTMLElement) => (event: DragEvent) => void
  onDragEnter: (element: HTMLElement) => (event: DragEvent) => void
  onDragLeave: (element: HTMLElement) => (event: DragEvent) => void
  isDragging: boolean
  draggedItem: FileSystemItem | null
}

export interface DragDropOptions {
  onFileMove?: (source: FileSystemItem, target: FileSystemItem) => void
  onFileCopy?: (source: FileSystemItem, target: FileSystemItem) => void
  onFileReorder?: (item: FileSystemItem, newIndex: number) => void
  onOperationComplete?: (result: DragDropResult) => void
  enableReordering?: boolean
  enableCopyOnCtrl?: boolean
}

export const useDragDrop = (options: DragDropOptions = {}): DragDropHandlers => {
  const {
    onFileMove,
    onFileCopy,
    onFileReorder,
    onOperationComplete,
    enableReordering = true,
    enableCopyOnCtrl = true
  } = options

  const { toast } = useToast()
  const dragDropManager = useRef(new DragDropManager())
  const [isDragging, setIsDragging] = useState(false)
  const [draggedItem, setDraggedItem] = useState<FileSystemItem | null>(null)

  /**
   * Handle drag start
   */
  const onDragStart = useCallback((item: FileSystemItem, element: HTMLElement) => {
    return (event: DragEvent) => {
      setIsDragging(true)
      setDraggedItem(item)
      
      // Store item data in element for retrieval
      element.dataset.itemId = String(item.id)
      element.dataset.itemPath = item.path || ''
      element.dataset.itemType = item.type
      element.dataset.itemName = item.name

      dragDropManager.current.startDrag(item, element, event)
    }
  }, [])

  /**
   * Handle drag over
   */
  const onDragOver = useCallback((element: HTMLElement) => {
    return (event: DragEvent) => {
      const handled = dragDropManager.current.handleDragOver(element, event)
      if (handled) {
        event.preventDefault()
      }
    }
  }, [])

  /**
   * Handle drop
   */
  const onDrop = useCallback((element: HTMLElement) => {
    return async (event: DragEvent) => {
      const result = await dragDropManager.current.handleDrop(element, event)
      
      if (result) {
        // Show toast notification
        if (result.success) {
          const operationText = result.operation === 'copy' ? 'Copied' : 
                               result.operation === 'move' ? 'Moved' : 'Reordered'
          toast({
            title: `${operationText} successfully`,
            description: `${result.source.name} ${result.operation === 'copy' ? 'copied to' : 
                         result.operation === 'move' ? 'moved to' : 'reordered in'} ${result.target.name}`,
          })

          // Call appropriate callback
          switch (result.operation) {
            case 'move':
              onFileMove?.(result.source, result.target)
              break
            case 'copy':
              onFileCopy?.(result.source, result.target)
              break
            case 'reorder':
              onFileReorder?.(result.source, 0) // Index would need to be calculated
              break
          }
        } else {
          toast({
            title: 'Operation failed',
            description: result.error || 'Unknown error occurred',
            variant: 'destructive'
          })
        }

        onOperationComplete?.(result)
      }

      setIsDragging(false)
      setDraggedItem(null)
    }
  }, [toast, onFileMove, onFileCopy, onFileReorder, onOperationComplete])

  /**
   * Handle drag end
   */
  const onDragEnd = useCallback((element: HTMLElement) => {
    return (event: DragEvent) => {
      dragDropManager.current.handleDragEnd(element)
      setIsDragging(false)
      setDraggedItem(null)
      
      // Clean up element data
      delete element.dataset.itemId
      delete element.dataset.itemPath
      delete element.dataset.itemType
      delete element.dataset.itemName
    }
  }, [])

  /**
   * Handle drag enter
   */
  const onDragEnter = useCallback((element: HTMLElement) => {
    return (event: DragEvent) => {
      event.preventDefault()
      element.classList.add('drag-over')
    }
  }, [])

  /**
   * Handle drag leave
   */
  const onDragLeave = useCallback((element: HTMLElement) => {
    return (event: DragEvent) => {
      // Only remove class if we're actually leaving the element
      if (!element.contains(event.relatedTarget as Node)) {
        element.classList.remove('drag-over')
      }
    }
  }, [])

  return {
    onDragStart,
    onDragOver,
    onDrop,
    onDragEnd,
    onDragEnter,
    onDragLeave,
    isDragging,
    draggedItem
  }
}

/**
 * Enhanced DragDropManager with better item resolution
 */
class EnhancedDragDropManager extends DragDropManager {
  /**
   * Get item from element using data attributes
   */
  protected getItemFromElement(element: HTMLElement): FileSystemItem | null {
    const itemId = element.dataset.itemId
    const itemPath = element.dataset.itemPath
    const itemType = element.dataset.itemType
    const itemName = element.dataset.itemName

    if (!itemId || !itemName || !itemType) return null

    return {
      id: itemId,
      name: itemName,
      type: itemType,
      path: itemPath || undefined
    }
  }

  /**
   * Get parent item from DOM hierarchy
   */
  protected getParentItem(element: HTMLElement): FileSystemItem | null {
    let parent = element.parentElement
    while (parent) {
      if (parent.dataset.itemId && parent.dataset.itemType === 'folder') {
        return this.getItemFromElement(parent)
      }
      parent = parent.parentElement
    }
    return null
  }
}

/**
 * Hook for draggable items
 */
export const useDraggable = (item: FileSystemItem, options: DragDropOptions = {}) => {
  const dragDrop = useDragDrop(options)
  
  return {
    draggable: true,
    onDragStart: (event: React.DragEvent<HTMLElement>) => {
      const element = event.currentTarget as HTMLElement
      dragDrop.onDragStart(item, element)(event.nativeEvent as DragEvent)
    },
    onDragEnd: (event: React.DragEvent<HTMLElement>) => {
      const element = event.currentTarget as HTMLElement
      dragDrop.onDragEnd(element)(event.nativeEvent as DragEvent)
    },
    className: dragDrop.isDragging && dragDrop.draggedItem?.id === item.id ? 'dragging' : ''
  }
}

/**
 * Hook for drop targets
 */
export const useDropTarget = (item: FileSystemItem, options: DragDropOptions = {}) => {
  const dragDrop = useDragDrop(options)
  
  return {
    onDragOver: (event: React.DragEvent<HTMLElement>) => {
      const element = event.currentTarget as HTMLElement
      dragDrop.onDragOver(element)(event.nativeEvent as DragEvent)
    },
    onDrop: (event: React.DragEvent<HTMLElement>) => {
      const element = event.currentTarget as HTMLElement
      dragDrop.onDrop(element)(event.nativeEvent as DragEvent)
    },
    onDragEnter: (event: React.DragEvent<HTMLElement>) => {
      const element = event.currentTarget as HTMLElement
      dragDrop.onDragEnter(element)(event.nativeEvent as DragEvent)
    },
    onDragLeave: (event: React.DragEvent<HTMLElement>) => {
      const element = event.currentTarget as HTMLElement
      dragDrop.onDragLeave(element)(event.nativeEvent as DragEvent)
    }
  }
}
