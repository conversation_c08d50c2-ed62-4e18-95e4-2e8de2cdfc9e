/**
 * File Context Menu Component
 * Provides context menu for file items with comprehensive actions
 */

import React, { useMemo, useCallback, useEffect, useState } from 'react'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuShortcut,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import {
  FileOpen,
  ExternalLink,
  Copy,
  Scissors,
  Clipboard,
  Edit,
  Trash,
  Link,
  FolderOpen,
  Settings,
  Files,
  Archive,
  GitBranch,
  Terminal
} from 'lucide-react'
import { ContextMenuProps, ContextMenuItem as MenuItemType, ContextMenuAction } from './types'
import { FileSystemItem } from '../types'
import { useClipboardManager } from './ClipboardManager'
import { cn } from "@/lib/utils"

/**
 * Calculate smart positioning for context menu to stay within viewport
 * Enhanced with better boundary detection and positioning logic
 */
function calculateSmartPosition(
  mouseX: number,
  mouseY: number,
  menuElement?: HTMLElement
): { x: number; y: number } {
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  const padding = 12 // Increased padding for better spacing

  // Use actual menu dimensions if available, otherwise use estimates
  const menuWidth = menuElement?.offsetWidth || 200
  const menuHeight = menuElement?.offsetHeight || 300

  let x = mouseX
  let y = mouseY

  // Smart horizontal positioning
  if (x + menuWidth + padding > viewportWidth) {
    // Try positioning to the left of cursor
    x = mouseX - menuWidth
    // If still overflows, align to right edge with padding
    if (x < padding) {
      x = viewportWidth - menuWidth - padding
    }
  }

  // Smart vertical positioning
  if (y + menuHeight + padding > viewportHeight) {
    // Try positioning above cursor
    y = mouseY - menuHeight
    // If still overflows, align to bottom edge with padding
    if (y < padding) {
      y = viewportHeight - menuHeight - padding
    }
  }

  // Final boundary checks
  x = Math.max(padding, Math.min(x, viewportWidth - menuWidth - padding))
  y = Math.max(padding, Math.min(y, viewportHeight - menuHeight - padding))

  return { x, y }
}

export const FileContextMenu: React.FC<ContextMenuProps> = ({
  item,
  position,
  onAction,
  onClose,
  visible
}) => {
  const clipboardManager = useClipboardManager()
  const canPaste = clipboardManager.canPaste()
  const [adjustedPosition, setAdjustedPosition] = useState(position)
  const [menuRef, setMenuRef] = useState<HTMLDivElement | null>(null)

  // Generate menu items - simplified to essential, working actions only
  const menuItems = useMemo((): MenuItemType[] => {
    const items: MenuItemType[] = []

    // Primary action
    items.push({
      id: 'open',
      label: 'Open',
      icon: FileOpen,
      shortcut: 'Enter'
    })

    // Separator
    items.push({ id: 'separator-1' as ContextMenuAction, label: '', separator: true })

    // Edit actions
    items.push(
      {
        id: 'copy',
        label: 'Copy',
        icon: Copy,
        shortcut: 'Ctrl+C'
      },
      {
        id: 'cut',
        label: 'Cut',
        icon: Scissors,
        shortcut: 'Ctrl+X'
      },
      {
        id: 'rename',
        label: 'Rename',
        icon: Edit,
        shortcut: 'F2'
      },
      {
        id: 'delete',
        label: 'Delete',
        icon: Trash,
        shortcut: 'Delete',
        dangerous: true
      }
    )

    // Separator
    items.push({ id: 'separator-2' as ContextMenuAction, label: '', separator: true })

    // Utility actions
    items.push(
      {
        id: 'copy-path',
        label: 'Copy Path',
        icon: Link
      },
      {
        id: 'reveal',
        label: 'Reveal in Explorer',
        icon: FolderOpen
      }
    )

    return items
  }, [item])

  const handleMenuItemClick = useCallback((action: ContextMenuAction) => {
    onAction(action, item)
    onClose()
  }, [onAction, item, onClose])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }, [onClose])

  // Calculate smart positioning when menu becomes visible or ref changes
  useEffect(() => {
    if (visible && menuRef) {
      const smartPos = calculateSmartPosition(position.x, position.y, menuRef)
      setAdjustedPosition(smartPos)
    }
  }, [visible, position.x, position.y, menuRef])

  if (!visible) return null

  return (
    <div
      className="fixed inset-0 z-50"
      onClick={onClose}
      onContextMenu={(e) => e.preventDefault()}
    >
      <div
        ref={setMenuRef}
        className="absolute bg-background border border-border rounded-md shadow-lg py-1 min-w-48 max-w-64 animate-in fade-in-0 zoom-in-95 duration-200"
        style={{
          left: adjustedPosition.x,
          top: adjustedPosition.y,
          zIndex: 1000
        }}
        onClick={(e) => e.stopPropagation()}
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        {menuItems.map((menuItem, index) => {
          if (menuItem.separator) {
            return (
              <div
                key={`separator-${index}`}
                className="context-menu-separator"
              />
            )
          }

          const IconComponent = menuItem.icon
          const isDisabled = menuItem.disabled

          return (
            <button
              key={menuItem.id}
              className={cn(
                "context-menu-item w-full px-3 py-2 text-left flex items-center gap-3",
                isDisabled && "opacity-50 cursor-not-allowed",
                menuItem.dangerous && "text-destructive hover:bg-destructive/10 focus:bg-destructive/10"
              )}
              onClick={() => !isDisabled && handleMenuItemClick(menuItem.id)}
              disabled={isDisabled}
            >
              {IconComponent && (
                <IconComponent className="h-4 w-4 flex-shrink-0" />
              )}
              <span className="flex-1">{menuItem.label}</span>
              {menuItem.shortcut && (
                <span className="text-xs text-muted-foreground font-mono">
                  {menuItem.shortcut}
                </span>
              )}
            </button>
          )
        })}
      </div>
    </div>
  )
}

/**
 * Utility function to check if file is an archive
 */
function isArchiveFile(filename: string): boolean {
  const archiveExtensions = ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2']
  return archiveExtensions.some(ext => filename.toLowerCase().endsWith(ext))
}

/**
 * Context menu wrapper component for easier usage
 */
export const FileContextMenuWrapper: React.FC<{
  children: React.ReactNode
  item: FileSystemItem
  onAction: (action: ContextMenuAction, item: FileSystemItem) => void
}> = ({ children, item, onAction }) => {
  const [contextMenu, setContextMenu] = React.useState<{
    visible: boolean
    position: { x: number; y: number }
  }>({
    visible: false,
    position: { x: 0, y: 0 }
  })

  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    setContextMenu({
      visible: true,
      position: { x: e.clientX, y: e.clientY }
    })
  }, [])

  const handleClose = useCallback(() => {
    setContextMenu(prev => ({ ...prev, visible: false }))
  }, [])

  const handleAction = useCallback((action: ContextMenuAction, fileItem: FileSystemItem) => {
    onAction(action, fileItem)
    handleClose()
  }, [onAction, handleClose])

  return (
    <>
      <div onContextMenu={handleContextMenu}>
        {children}
      </div>
      
      <FileContextMenu
        item={item}
        position={contextMenu.position}
        onAction={handleAction}
        onClose={handleClose}
        visible={contextMenu.visible}
      />
    </>
  )
}

export default FileContextMenu
