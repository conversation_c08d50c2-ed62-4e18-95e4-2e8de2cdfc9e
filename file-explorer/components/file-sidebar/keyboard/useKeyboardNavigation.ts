/**
 * Keyboard Navigation Hook
 * React hook for integrating keyboard navigation with file explorer components
 */

import { useEffect, useRef, useCallback, useState } from 'react'
import { FileSystemItem } from '../types'
import { KeyboardNavigationManager, KeyboardAction, NavigationState } from './KeyboardNavigationManager'

export interface KeyboardNavigationOptions {
  onFileSelect?: (file: FileSystemItem) => void
  onFolderToggle?: (folder: FileSystemItem) => void
  onFileDelete?: (file: FileSystemItem) => void
  onFileRename?: (file: FileSystemItem) => void
  onFileCopy?: (file: FileSystemItem) => void
  onFileCut?: (file: FileSystemItem) => void
  onFilePaste?: () => void
  onSearchFocus?: () => void
  enabled?: boolean
}

export interface KeyboardNavigationResult {
  selectedItem: FileSystemItem | null
  selectedIndex: number
  handleKeyDown: (event: React.KeyboardEvent) => void
  setSelectedItem: (item: FileSystemItem | null) => void
  focusExplorer: () => void
  navigationState: NavigationState
}

export const useKeyboardNavigation = (
  items: FileSystemItem[],
  options: KeyboardNavigationOptions = {}
): KeyboardNavigationResult => {
  const {
    onFileSelect,
    onFolderToggle,
    onFileDelete,
    onFileRename,
    onFileCopy,
    onFileCut,
    onFilePaste,
    onSearchFocus,
    enabled = true
  } = options

  const [navigationState, setNavigationState] = useState<NavigationState>({
    selectedIndex: -1,
    selectedItem: null,
    focusedElement: null,
    expandedFolders: new Set()
  })

  const managerRef = useRef<KeyboardNavigationManager>()
  const containerRef = useRef<HTMLElement>()

  // Initialize keyboard navigation manager
  useEffect(() => {
    if (!managerRef.current) {
      managerRef.current = KeyboardNavigationManager.getInstance()
    }

    const manager = managerRef.current
    const listenerId = `keyboard-nav-${Date.now()}`

    // Add action listener
    manager.addListener(listenerId, handleKeyboardAction)

    return () => {
      manager.removeListener(listenerId)
    }
  }, [])

  // Update items when they change
  useEffect(() => {
    if (managerRef.current && enabled) {
      managerRef.current.updateItems(items)
      setNavigationState(managerRef.current.getNavigationState())
    }
  }, [items, enabled])

  /**
   * Handle keyboard actions from the manager
   */
  const handleKeyboardAction = useCallback((action: KeyboardAction) => {
    switch (action.type) {
      case 'navigate':
        if (action.payload?.selectedItem !== undefined) {
          setNavigationState(managerRef.current!.getNavigationState())
          
          // Scroll selected item into view
          scrollToSelectedItem(action.payload.selectedIndex)
        }
        break

      case 'select':
        if (action.payload?.item) {
          const item = action.payload.item as FileSystemItem
          
          if (item.type === 'folder') {
            onFolderToggle?.(item)
          } else {
            onFileSelect?.(item)
          }
        }
        break

      case 'expand':
        if (action.payload?.item) {
          onFolderToggle?.(action.payload.item)
        }
        break

      case 'collapse':
        if (action.payload?.item) {
          onFolderToggle?.(action.payload.item)
        }
        break

      case 'delete':
        if (navigationState.selectedItem) {
          onFileDelete?.(navigationState.selectedItem)
        }
        break

      case 'rename':
        if (navigationState.selectedItem) {
          onFileRename?.(navigationState.selectedItem)
        }
        break

      case 'copy':
        if (navigationState.selectedItem) {
          onFileCopy?.(navigationState.selectedItem)
        }
        break

      case 'cut':
        if (navigationState.selectedItem) {
          onFileCut?.(navigationState.selectedItem)
        }
        break

      case 'paste':
        onFilePaste?.()
        break

      case 'search':
        onSearchFocus?.()
        break
    }
  }, [navigationState, onFileSelect, onFolderToggle, onFileDelete, onFileRename, onFileCopy, onFileCut, onFilePaste, onSearchFocus])

  /**
   * Scroll selected item into view
   */
  const scrollToSelectedItem = useCallback((index: number) => {
    if (!containerRef.current) return

    const items = containerRef.current.querySelectorAll('[data-keyboard-nav-item]')
    const selectedElement = items[index] as HTMLElement

    if (selectedElement) {
      selectedElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      })
    }
  }, [])

  /**
   * Handle keyboard events
   */
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (!enabled || !managerRef.current) return

    // Only handle if the file explorer has focus or no other input is focused
    const activeElement = document.activeElement
    const isInputFocused = activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.contentEditable === 'true'
    )

    if (isInputFocused && event.key !== 'Escape') return

    const handled = managerRef.current.handleKeyboardEvent(event.nativeEvent)
    
    if (handled) {
      event.preventDefault()
      event.stopPropagation()
    }
  }, [enabled])

  /**
   * Set selected item programmatically
   */
  const setSelectedItem = useCallback((item: FileSystemItem | null) => {
    if (managerRef.current) {
      managerRef.current.setSelectedItem(item)
      setNavigationState(managerRef.current.getNavigationState())
    }
  }, [])

  /**
   * Focus the file explorer for keyboard navigation
   */
  const focusExplorer = useCallback(() => {
    if (containerRef.current && managerRef.current) {
      managerRef.current.focus(containerRef.current)
    }
  }, [])

  // Set container ref for scrolling
  useEffect(() => {
    const container = document.querySelector('[data-keyboard-nav-container]') as HTMLElement
    if (container) {
      containerRef.current = container
    }
  }, [])

  return {
    selectedItem: navigationState.selectedItem,
    selectedIndex: navigationState.selectedIndex,
    handleKeyDown,
    setSelectedItem,
    focusExplorer,
    navigationState
  }
}

/**
 * Hook for keyboard shortcuts display
 */
export const useKeyboardShortcuts = () => {
  const [shortcuts, setShortcuts] = useState<any[]>([])

  useEffect(() => {
    const manager = KeyboardNavigationManager.getInstance()
    setShortcuts(manager.getShortcuts())
  }, [])

  const getShortcutsByCategory = useCallback((category: string) => {
    return shortcuts.filter(shortcut => shortcut.category === category)
  }, [shortcuts])

  const formatShortcut = useCallback((shortcut: any) => {
    const parts = []
    
    if (shortcut.ctrlKey || shortcut.metaKey) {
      parts.push(navigator.platform.includes('Mac') ? '⌘' : 'Ctrl')
    }
    if (shortcut.shiftKey) parts.push('Shift')
    if (shortcut.altKey) parts.push('Alt')
    
    parts.push(shortcut.key === ' ' ? 'Space' : shortcut.key)
    
    return parts.join(' + ')
  }, [])

  return {
    shortcuts,
    getShortcutsByCategory,
    formatShortcut
  }
}

/**
 * Hook for managing keyboard navigation focus
 */
export const useKeyboardFocus = () => {
  const [hasFocus, setHasFocus] = useState(false)
  const managerRef = useRef<KeyboardNavigationManager>()

  useEffect(() => {
    if (!managerRef.current) {
      managerRef.current = KeyboardNavigationManager.getInstance()
    }

    const checkFocus = () => {
      setHasFocus(managerRef.current!.hasFocus())
    }

    // Check focus periodically
    const interval = setInterval(checkFocus, 100)

    return () => clearInterval(interval)
  }, [])

  const focus = useCallback((element?: HTMLElement) => {
    if (managerRef.current) {
      managerRef.current.focus(element)
      setHasFocus(true)
    }
  }, [])

  return { hasFocus, focus }
}
