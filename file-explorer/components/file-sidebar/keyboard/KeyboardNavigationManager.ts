/**
 * Keyboard Navigation Manager
 * Handles comprehensive keyboard shortcuts and navigation for file explorer
 */

import { FileSystemItem } from '../types'

export interface NavigationState {
  selectedIndex: number
  selectedItem: FileSystemItem | null
  focusedElement: HTMLElement | null
  expandedFolders: Set<string | number>
}

export interface KeyboardAction {
  type: 'navigate' | 'select' | 'expand' | 'collapse' | 'delete' | 'rename' | 'copy' | 'cut' | 'paste' | 'search'
  payload?: any
}

export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
  metaKey?: boolean
  action: KeyboardAction
  description: string
  category: 'navigation' | 'selection' | 'editing' | 'operations'
}

export class KeyboardNavigationManager {
  private static instance: KeyboardNavigationManager
  private navigationState: NavigationState
  private flattenedItems: FileSystemItem[] = []
  private shortcuts: KeyboardShortcut[] = []
  private listeners: Map<string, (action: KeyboardAction) => void> = new Map()

  constructor() {
    this.navigationState = {
      selectedIndex: -1,
      selectedItem: null,
      focusedElement: null,
      expandedFolders: new Set()
    }
    this.initializeShortcuts()
  }

  static getInstance(): KeyboardNavigationManager {
    if (!KeyboardNavigationManager.instance) {
      KeyboardNavigationManager.instance = new KeyboardNavigationManager()
    }
    return KeyboardNavigationManager.instance
  }

  /**
   * Initialize keyboard shortcuts
   */
  private initializeShortcuts(): void {
    this.shortcuts = [
      // Navigation shortcuts
      {
        key: 'ArrowDown',
        action: { type: 'navigate', payload: { direction: 'down' } },
        description: 'Navigate down',
        category: 'navigation'
      },
      {
        key: 'ArrowUp',
        action: { type: 'navigate', payload: { direction: 'up' } },
        description: 'Navigate up',
        category: 'navigation'
      },
      {
        key: 'ArrowRight',
        action: { type: 'expand' },
        description: 'Expand folder or navigate into',
        category: 'navigation'
      },
      {
        key: 'ArrowLeft',
        action: { type: 'collapse' },
        description: 'Collapse folder or navigate out',
        category: 'navigation'
      },
      {
        key: 'Home',
        action: { type: 'navigate', payload: { direction: 'first' } },
        description: 'Go to first item',
        category: 'navigation'
      },
      {
        key: 'End',
        action: { type: 'navigate', payload: { direction: 'last' } },
        description: 'Go to last item',
        category: 'navigation'
      },
      {
        key: 'PageDown',
        action: { type: 'navigate', payload: { direction: 'pageDown' } },
        description: 'Navigate page down',
        category: 'navigation'
      },
      {
        key: 'PageUp',
        action: { type: 'navigate', payload: { direction: 'pageUp' } },
        description: 'Navigate page up',
        category: 'navigation'
      },

      // Selection shortcuts
      {
        key: 'Enter',
        action: { type: 'select' },
        description: 'Open/select item',
        category: 'selection'
      },
      {
        key: ' ',
        action: { type: 'select', payload: { toggle: true } },
        description: 'Toggle selection',
        category: 'selection'
      },

      // File operations
      {
        key: 'Delete',
        action: { type: 'delete' },
        description: 'Delete selected item',
        category: 'operations'
      },
      {
        key: 'F2',
        action: { type: 'rename' },
        description: 'Rename selected item',
        category: 'operations'
      },
      {
        key: 'c',
        ctrlKey: true,
        action: { type: 'copy' },
        description: 'Copy selected item',
        category: 'operations'
      },
      {
        key: 'x',
        ctrlKey: true,
        action: { type: 'cut' },
        description: 'Cut selected item',
        category: 'operations'
      },
      {
        key: 'v',
        ctrlKey: true,
        action: { type: 'paste' },
        description: 'Paste item',
        category: 'operations'
      },

      // Search and other
      {
        key: 'f',
        ctrlKey: true,
        action: { type: 'search' },
        description: 'Focus search',
        category: 'navigation'
      },
      {
        key: 'Escape',
        action: { type: 'navigate', payload: { direction: 'escape' } },
        description: 'Clear selection/focus',
        category: 'navigation'
      }
    ]
  }

  /**
   * Update the flattened items list for navigation
   */
  updateItems(items: FileSystemItem[]): void {
    this.flattenedItems = this.flattenItems(items)
    
    // Adjust selected index if it's out of bounds
    if (this.navigationState.selectedIndex >= this.flattenedItems.length) {
      this.navigationState.selectedIndex = Math.max(0, this.flattenedItems.length - 1)
    }
    
    // Update selected item
    if (this.navigationState.selectedIndex >= 0 && this.navigationState.selectedIndex < this.flattenedItems.length) {
      this.navigationState.selectedItem = this.flattenedItems[this.navigationState.selectedIndex]
    } else {
      this.navigationState.selectedItem = null
    }
  }

  /**
   * Flatten items for linear navigation
   */
  private flattenItems(items: FileSystemItem[], level: number = 0): FileSystemItem[] {
    const flattened: FileSystemItem[] = []
    
    for (const item of items) {
      flattened.push({ ...item, level })
      
      if (item.type === 'folder' && item.expanded && item.files) {
        flattened.push(...this.flattenItems(item.files, level + 1))
      }
    }
    
    return flattened
  }

  /**
   * Handle keyboard event
   */
  handleKeyboardEvent(event: KeyboardEvent): boolean {
    const shortcut = this.findMatchingShortcut(event)
    
    if (shortcut) {
      event.preventDefault()
      event.stopPropagation()
      
      this.executeAction(shortcut.action)
      return true
    }
    
    return false
  }

  /**
   * Find matching shortcut for keyboard event
   */
  private findMatchingShortcut(event: KeyboardEvent): KeyboardShortcut | null {
    return this.shortcuts.find(shortcut => {
      return shortcut.key === event.key &&
             (shortcut.ctrlKey || false) === event.ctrlKey &&
             (shortcut.shiftKey || false) === event.shiftKey &&
             (shortcut.altKey || false) === event.altKey &&
             (shortcut.metaKey || false) === event.metaKey
    }) || null
  }

  /**
   * Execute keyboard action
   */
  private executeAction(action: KeyboardAction): void {
    switch (action.type) {
      case 'navigate':
        this.handleNavigation(action.payload)
        break
      case 'select':
        this.handleSelection(action.payload)
        break
      case 'expand':
        this.handleExpand()
        break
      case 'collapse':
        this.handleCollapse()
        break
      default:
        // Notify listeners for other actions
        this.notifyListeners(action)
        break
    }
  }

  /**
   * Handle navigation actions
   */
  private handleNavigation(payload: any): void {
    const direction = payload?.direction
    const oldIndex = this.navigationState.selectedIndex
    
    switch (direction) {
      case 'down':
        this.navigationState.selectedIndex = Math.min(
          this.navigationState.selectedIndex + 1,
          this.flattenedItems.length - 1
        )
        break
      case 'up':
        this.navigationState.selectedIndex = Math.max(
          this.navigationState.selectedIndex - 1,
          0
        )
        break
      case 'first':
        this.navigationState.selectedIndex = 0
        break
      case 'last':
        this.navigationState.selectedIndex = this.flattenedItems.length - 1
        break
      case 'pageDown':
        this.navigationState.selectedIndex = Math.min(
          this.navigationState.selectedIndex + 10,
          this.flattenedItems.length - 1
        )
        break
      case 'pageUp':
        this.navigationState.selectedIndex = Math.max(
          this.navigationState.selectedIndex - 10,
          0
        )
        break
      case 'escape':
        this.navigationState.selectedIndex = -1
        this.navigationState.selectedItem = null
        break
    }
    
    // Update selected item
    if (this.navigationState.selectedIndex >= 0 && this.navigationState.selectedIndex < this.flattenedItems.length) {
      this.navigationState.selectedItem = this.flattenedItems[this.navigationState.selectedIndex]
    } else {
      this.navigationState.selectedItem = null
    }
    
    // Notify listeners if selection changed
    if (oldIndex !== this.navigationState.selectedIndex) {
      this.notifyListeners({
        type: 'navigate',
        payload: {
          selectedItem: this.navigationState.selectedItem,
          selectedIndex: this.navigationState.selectedIndex
        }
      })
    }
  }

  /**
   * Handle selection actions
   */
  private handleSelection(payload: any): void {
    if (this.navigationState.selectedItem) {
      this.notifyListeners({
        type: 'select',
        payload: {
          item: this.navigationState.selectedItem,
          toggle: payload?.toggle || false
        }
      })
    }
  }

  /**
   * Handle expand action
   */
  private handleExpand(): void {
    if (this.navigationState.selectedItem && this.navigationState.selectedItem.type === 'folder') {
      this.notifyListeners({
        type: 'expand',
        payload: { item: this.navigationState.selectedItem }
      })
    }
  }

  /**
   * Handle collapse action
   */
  private handleCollapse(): void {
    if (this.navigationState.selectedItem && this.navigationState.selectedItem.type === 'folder') {
      this.notifyListeners({
        type: 'collapse',
        payload: { item: this.navigationState.selectedItem }
      })
    }
  }

  /**
   * Add action listener
   */
  addListener(id: string, callback: (action: KeyboardAction) => void): void {
    this.listeners.set(id, callback)
  }

  /**
   * Remove action listener
   */
  removeListener(id: string): void {
    this.listeners.delete(id)
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(action: KeyboardAction): void {
    this.listeners.forEach(callback => callback(action))
  }

  /**
   * Get current navigation state
   */
  getNavigationState(): NavigationState {
    return { ...this.navigationState }
  }

  /**
   * Set selected item programmatically
   */
  setSelectedItem(item: FileSystemItem | null): void {
    if (item) {
      const index = this.flattenedItems.findIndex(i => i.id === item.id)
      if (index >= 0) {
        this.navigationState.selectedIndex = index
        this.navigationState.selectedItem = item
      }
    } else {
      this.navigationState.selectedIndex = -1
      this.navigationState.selectedItem = null
    }
  }

  /**
   * Get all keyboard shortcuts
   */
  getShortcuts(): KeyboardShortcut[] {
    return [...this.shortcuts]
  }

  /**
   * Focus file explorer for keyboard navigation
   */
  focus(element?: HTMLElement): void {
    if (element) {
      this.navigationState.focusedElement = element
      element.focus()
    }
  }

  /**
   * Check if file explorer has focus
   */
  hasFocus(): boolean {
    return this.navigationState.focusedElement === document.activeElement
  }
}
