/**
 * Keyboard Shortcuts Help Component
 * Displays available keyboard shortcuts in a help dialog
 */

import React from 'react'
import { Keyboard, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { cn } from "@/lib/utils"
import { useKeyboardShortcuts } from './useKeyboardNavigation'

interface KeyboardShortcutsHelpProps {
  isOpen: boolean
  onClose: () => void
}

export const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({
  isOpen,
  onClose
}) => {
  const { shortcuts, getShortcutsByCategory, formatShortcut } = useKeyboardShortcuts()

  if (!isOpen) return null

  const categories = [
    { id: 'navigation', title: 'Navigation', icon: '🧭' },
    { id: 'selection', title: 'Selection', icon: '🎯' },
    { id: 'operations', title: 'File Operations', icon: '📁' },
    { id: 'editing', title: 'Editing', icon: '✏️' }
  ]

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-2xl bg-background border border-border rounded-lg shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border">
            <div className="flex items-center gap-2">
              <Keyboard className="w-5 h-5 text-primary" />
              <h2 className="text-lg font-semibold">Keyboard Shortcuts</h2>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Content */}
          <ScrollArea className="max-h-96 p-4">
            <div className="space-y-6">
              {categories.map(category => {
                const categoryShortcuts = getShortcutsByCategory(category.id)
                
                if (categoryShortcuts.length === 0) return null

                return (
                  <div key={category.id}>
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-lg">{category.icon}</span>
                      <h3 className="font-medium text-foreground">{category.title}</h3>
                    </div>
                    
                    <div className="space-y-2">
                      {categoryShortcuts.map((shortcut, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between py-2 px-3 rounded-md hover:bg-muted/50 transition-colors"
                        >
                          <span className="text-sm text-muted-foreground">
                            {shortcut.description}
                          </span>
                          <Badge variant="outline" className="font-mono text-xs">
                            {formatShortcut(shortcut)}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Additional Tips */}
            <div className="mt-8 p-4 bg-muted/30 rounded-md">
              <h4 className="font-medium mb-2 text-sm">💡 Tips</h4>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Use Tab to focus the file explorer for keyboard navigation</li>
                <li>• Arrow keys work when the file explorer is focused</li>
                <li>• Hold Shift while navigating to select multiple items</li>
                <li>• Press Escape to clear selection and return focus</li>
                <li>• Use Ctrl+F to quickly focus the search box</li>
              </ul>
            </div>
          </ScrollArea>

          {/* Footer */}
          <div className="flex justify-end p-4 border-t border-border">
            <Button onClick={onClose}>Close</Button>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * Keyboard Shortcuts Button Component
 */
interface KeyboardShortcutsButtonProps {
  onClick: () => void
  className?: string
}

export const KeyboardShortcutsButton: React.FC<KeyboardShortcutsButtonProps> = ({
  onClick,
  className
}) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      className={cn("h-8 w-8 p-0", className)}
      title="Keyboard Shortcuts (F1)"
    >
      <Keyboard className="w-4 h-4" />
    </Button>
  )
}

/**
 * Inline Shortcut Display Component
 */
interface ShortcutDisplayProps {
  shortcut: string
  className?: string
}

export const ShortcutDisplay: React.FC<ShortcutDisplayProps> = ({
  shortcut,
  className
}) => {
  return (
    <Badge
      variant="outline"
      className={cn("font-mono text-xs", className)}
    >
      {shortcut}
    </Badge>
  )
}

/**
 * Keyboard Navigation Status Component
 */
interface NavigationStatusProps {
  isActive: boolean
  selectedItem: string | null
  className?: string
}

export const NavigationStatus: React.FC<NavigationStatusProps> = ({
  isActive,
  selectedItem,
  className
}) => {
  return (
    <div className={cn("flex items-center gap-2 text-xs text-muted-foreground", className)}>
      <div className={cn(
        "w-2 h-2 rounded-full",
        isActive ? "bg-green-500" : "bg-gray-400"
      )} />
      <span>
        {isActive ? 'Keyboard navigation active' : 'Click to enable keyboard navigation'}
      </span>
      {selectedItem && (
        <span className="ml-2 font-medium">
          Selected: {selectedItem}
        </span>
      )}
    </div>
  )
}

/**
 * Quick Shortcut Hints Component
 */
export const QuickShortcutHints: React.FC = () => {
  return (
    <div className="flex items-center gap-4 text-xs text-muted-foreground/70">
      <div className="flex items-center gap-1">
        <ShortcutDisplay shortcut="↑↓" className="h-4 text-[10px]" />
        <span>Navigate</span>
      </div>
      <div className="flex items-center gap-1">
        <ShortcutDisplay shortcut="Enter" className="h-4 text-[10px]" />
        <span>Open</span>
      </div>
      <div className="flex items-center gap-1">
        <ShortcutDisplay shortcut="F2" className="h-4 text-[10px]" />
        <span>Rename</span>
      </div>
      <div className="flex items-center gap-1">
        <ShortcutDisplay shortcut="Del" className="h-4 text-[10px]" />
        <span>Delete</span>
      </div>
    </div>
  )
}

export default KeyboardShortcutsHelp
