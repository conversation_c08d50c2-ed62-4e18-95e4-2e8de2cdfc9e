/**
 * Keyboard Navigation Demo Component
 * Demonstrates keyboard navigation features with interactive examples
 */

import React, { useState } from 'react'
import { Keyboard, Play, Info } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from "@/lib/utils"
import { useKeyboardShortcuts } from './useKeyboardNavigation'
import { ShortcutDisplay, QuickShortcutHints } from './KeyboardShortcutsHelp'

export const KeyboardNavigationDemo: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<string | null>(null)
  const { shortcuts, getShortcutsByCategory, formatShortcut } = useKeyboardShortcuts()

  const demoSections = [
    {
      id: 'navigation',
      title: 'Navigation',
      description: 'Move through files and folders',
      icon: '🧭',
      shortcuts: getShortcutsByCategory('navigation')
    },
    {
      id: 'selection',
      title: 'Selection',
      description: 'Select and open files',
      icon: '🎯',
      shortcuts: getShortcutsByCategory('selection')
    },
    {
      id: 'operations',
      title: 'File Operations',
      description: 'Copy, cut, paste, delete files',
      icon: '📁',
      shortcuts: getShortcutsByCategory('operations')
    }
  ]

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Keyboard className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-bold">Keyboard Navigation Demo</h2>
        </div>
        <p className="text-muted-foreground">
          Learn how to navigate the file explorer efficiently using keyboard shortcuts
        </p>
      </div>

      {/* Quick Reference */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="w-4 h-4" />
            Quick Reference
          </CardTitle>
          <CardDescription>
            Most commonly used shortcuts for file explorer navigation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <QuickShortcutHints />
        </CardContent>
      </Card>

      {/* Demo Sections */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {demoSections.map((section) => (
          <Card 
            key={section.id}
            className={cn(
              "cursor-pointer transition-all duration-200",
              activeDemo === section.id && "ring-2 ring-primary"
            )}
            onClick={() => setActiveDemo(activeDemo === section.id ? null : section.id)}
          >
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <span className="text-xl">{section.icon}</span>
                {section.title}
              </CardTitle>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            
            {activeDemo === section.id && (
              <CardContent>
                <div className="space-y-2">
                  {section.shortcuts.map((shortcut, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 rounded-md bg-muted/30"
                    >
                      <span className="text-sm">{shortcut.description}</span>
                      <ShortcutDisplay shortcut={formatShortcut(shortcut)} />
                    </div>
                  ))}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* Interactive Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="w-4 h-4" />
            Interactive Demo
          </CardTitle>
          <CardDescription>
            Try the keyboard navigation in a simulated file explorer
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 border border-border rounded-md bg-muted/20">
              <p className="text-sm text-muted-foreground mb-3">
                Click in the area below and use keyboard shortcuts to navigate:
              </p>
              
              <div 
                className="min-h-32 p-3 border border-dashed border-border rounded-md bg-background focus:ring-2 focus:ring-primary/50 outline-none"
                tabIndex={0}
              >
                <div className="space-y-1">
                  <div className="flex items-center gap-2 p-2 rounded hover:bg-accent/50" tabIndex={-1}>
                    <span>📁</span>
                    <span>src</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded hover:bg-accent/50 ml-4" tabIndex={-1}>
                    <span>📄</span>
                    <span>index.ts</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded hover:bg-accent/50 ml-4" tabIndex={-1}>
                    <span>📄</span>
                    <span>utils.ts</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded hover:bg-accent/50" tabIndex={-1}>
                    <span>📁</span>
                    <span>docs</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded hover:bg-accent/50 ml-4" tabIndex={-1}>
                    <span>📄</span>
                    <span>README.md</span>
                  </div>
                </div>
              </div>
              
              <div className="mt-3 text-xs text-muted-foreground">
                <p>• Use ↑↓ arrow keys to navigate</p>
                <p>• Press Enter to select</p>
                <p>• Press Tab to focus this demo area</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tips and Best Practices */}
      <Card>
        <CardHeader>
          <CardTitle>💡 Tips & Best Practices</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">Getting Started</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Click on the file explorer to focus it</li>
                <li>• Use Tab to move focus between UI elements</li>
                <li>• Press F1 to see all available shortcuts</li>
                <li>• Use Escape to clear selection</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Advanced Navigation</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Hold Shift while navigating to select multiple items</li>
                <li>• Use Page Up/Down for faster navigation</li>
                <li>• Home/End keys jump to first/last items</li>
                <li>• Ctrl+F focuses the search box instantly</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Accessibility Features */}
      <Card>
        <CardHeader>
          <CardTitle>♿ Accessibility Features</CardTitle>
          <CardDescription>
            Built-in accessibility support for all users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <Badge variant="outline" className="mt-0.5">Screen Readers</Badge>
              <div className="text-sm">
                <p className="font-medium">Full screen reader support</p>
                <p className="text-muted-foreground">All navigation actions are announced with proper ARIA labels</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <Badge variant="outline" className="mt-0.5">High Contrast</Badge>
              <div className="text-sm">
                <p className="font-medium">High contrast mode support</p>
                <p className="text-muted-foreground">Enhanced focus indicators for users with visual impairments</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <Badge variant="outline" className="mt-0.5">Motor Accessibility</Badge>
              <div className="text-sm">
                <p className="font-medium">Keyboard-only navigation</p>
                <p className="text-muted-foreground">Complete functionality without requiring mouse interaction</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default KeyboardNavigationDemo
