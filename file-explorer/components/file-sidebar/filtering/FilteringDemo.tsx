/**
 * File Filtering Demo Component
 * Demonstrates file filtering capabilities with interactive examples
 */

import React, { useState } from 'react'
import { Filter, BarChart3, Info, Shuffle, Eye, EyeOff } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from "@/lib/utils"
import { FileSystemItem } from '../types'
import { useFileFiltering, useFilterIndicators } from './useFileFiltering'
import { FilteringControls, QuickFilterButtons } from './FilteringControls'

// Sample files for demonstration
const sampleFiles: FileSystemItem[] = [
  {
    id: 'folder1',
    name: 'src',
    type: 'folder',
    path: '/project/src',
    files: [
      { id: 'file1', name: 'index.ts', type: 'ts', path: '/project/src/index.ts' },
      { id: 'file2', name: 'App.tsx', type: 'tsx', path: '/project/src/App.tsx' },
      { id: 'file3', name: 'utils.js', type: 'js', path: '/project/src/utils.js' },
      { id: 'file4', name: 'styles.css', type: 'css', path: '/project/src/styles.css' },
      { id: 'file5', name: '.env', type: 'env', path: '/project/src/.env' },
      { id: 'file6', name: '_private.ts', type: 'ts', path: '/project/src/_private.ts' }
    ]
  },
  {
    id: 'folder2',
    name: 'assets',
    type: 'folder',
    path: '/project/assets',
    files: [
      { id: 'file7', name: 'logo.png', type: 'png', path: '/project/assets/logo.png' },
      { id: 'file8', name: 'banner.jpg', type: 'jpg', path: '/project/assets/banner.jpg' },
      { id: 'file9', name: 'icon.svg', type: 'svg', path: '/project/assets/icon.svg' },
      { id: 'file10', name: 'video.mp4', type: 'mp4', path: '/project/assets/video.mp4' }
    ]
  },
  {
    id: 'folder3',
    name: 'docs',
    type: 'folder',
    path: '/project/docs',
    files: [
      { id: 'file11', name: 'README.md', type: 'md', path: '/project/docs/README.md' },
      { id: 'file12', name: 'API.md', type: 'md', path: '/project/docs/API.md' },
      { id: 'file13', name: 'guide.pdf', type: 'pdf', path: '/project/docs/guide.pdf' },
      { id: 'file14', name: 'manual.docx', type: 'docx', path: '/project/docs/manual.docx' }
    ]
  },
  {
    id: 'folder4',
    name: 'empty-folder',
    type: 'folder',
    path: '/project/empty-folder',
    files: []
  },
  { id: 'file15', name: 'package.json', type: 'json', path: '/project/package.json' },
  { id: 'file16', name: 'tsconfig.json', type: 'json', path: '/project/tsconfig.json' },
  { id: 'file17', name: 'webpack.config.js', type: 'js', path: '/project/webpack.config.js' },
  { id: 'file18', name: 'data.zip', type: 'zip', path: '/project/data.zip' },
  { id: 'file19', name: '.gitignore', type: 'gitignore', path: '/project/.gitignore' },
  { id: 'file20', name: '_temp.txt', type: 'txt', path: '/project/_temp.txt' }
]

export const FilteringDemo: React.FC = () => {
  const [selectedFiles, setSelectedFiles] = useState<FileSystemItem[]>(sampleFiles)
  const [showDetails, setShowDetails] = useState(false)

  const fileFiltering = useFileFiltering(selectedFiles, {
    autoFilter: true,
    persistConfig: false,
    onFilterChange: (config) => {
      console.log('Filter configuration changed:', config)
    }
  })

  const { formatFilterConfig, getActiveFilterCount } = useFilterIndicators(
    fileFiltering.filterConfig, 
    fileFiltering.filterStats
  )

  const shuffleFiles = () => {
    const shuffled = [...selectedFiles].sort(() => Math.random() - 0.5)
    setSelectedFiles(shuffled)
  }

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Filter className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-bold">File Filtering Demo</h2>
        </div>
        <p className="text-muted-foreground">
          Explore different filtering options and see how files are organized
        </p>
      </div>

      {/* Filter Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            Filter Controls
          </CardTitle>
          <CardDescription>
            Try different filter categories and advanced options
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Filtering Controls */}
          <FilteringControls
            filterConfig={fileFiltering.filterConfig}
            filterCategories={fileFiltering.filterCategories}
            filterStats={fileFiltering.filterStats}
            onToggleFilter={fileFiltering.toggleFilter}
            onUpdateConfig={fileFiltering.updateFilterConfig}
            onResetFilters={fileFiltering.resetFilters}
            compact={false}
          />

          {/* Demo Actions */}
          <div className="flex items-center gap-2 pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={shuffleFiles}
              className="gap-1"
            >
              <Shuffle className="w-3 h-3" />
              Shuffle Files
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="gap-1"
            >
              {showDetails ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
              {showDetails ? 'Hide' : 'Show'} Details
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Filter Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Filter Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {fileFiltering.filterCategories.map((category) => (
              <div key={category.id} className="text-center">
                <div className="text-2xl mb-1">{category.icon}</div>
                <div className="font-medium text-sm">{category.label}</div>
                <Badge variant="outline" className="text-xs">
                  {fileFiltering.filterStats[category.id]} files
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Filter Status */}
      <Card>
        <CardHeader>
          <CardTitle>Current Filter Status</CardTitle>
          <CardDescription>
            {formatFilterConfig()} • {fileFiltering.filteredFiles.length} of {selectedFiles.length} items shown
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 flex-wrap">
            <Badge variant="outline">
              Total: {selectedFiles.length} items
            </Badge>
            <Badge variant="outline">
              Filtered: {fileFiltering.filteredFiles.length} items
            </Badge>
            <Badge variant="outline">
              Hidden: {selectedFiles.length - fileFiltering.filteredFiles.length} items
            </Badge>
            {getActiveFilterCount() > 0 && (
              <Badge variant="secondary">
                {getActiveFilterCount()} active filters
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Filtered Files Display */}
      <Card>
        <CardHeader>
          <CardTitle>Filtered Files</CardTitle>
          <CardDescription>
            Files matching current filter criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {fileFiltering.filteredFiles.map((item) => (
              <FilteredFileItem
                key={item.id}
                item={item}
                showDetails={showDetails}
              />
            ))}
            
            {fileFiltering.filteredFiles.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Filter className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No files match the current filter criteria</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fileFiltering.resetFilters}
                  className="mt-2"
                >
                  Reset Filters
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Filter Information */}
      <Card>
        <CardHeader>
          <CardTitle>How Filtering Works</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">🏷️ Category Filters</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Files are categorized by their extensions into logical groups
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Code: .js, .ts, .py, .java, etc.</li>
                <li>• Images: .png, .jpg, .svg, etc.</li>
                <li>• Documents: .pdf, .doc, .md, etc.</li>
                <li>• Media: .mp4, .mp3, .avi, etc.</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">👁️ Visibility Options</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Control which files and folders are visible
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Hidden files: Files starting with . or _</li>
                <li>• Empty folders: Folders with no content</li>
                <li>• Pattern matching: Wildcard support (*)</li>
                <li>• Size filtering: Min/max file sizes</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">🔍 Advanced Filters</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Powerful filtering options for specific needs
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Name patterns: *.js, test*, etc.</li>
                <li>• Size ranges: Filter by file size</li>
                <li>• Date ranges: Filter by modification date</li>
                <li>• Exclude patterns: Hide specific files</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">⚡ Real-time Updates</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Filters are applied instantly as you change them
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Immediate visual feedback</li>
                <li>• Persistent filter settings</li>
                <li>• Combine multiple filters</li>
                <li>• Reset to defaults anytime</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Individual filtered file item component
 */
const FilteredFileItem: React.FC<{
  item: FileSystemItem
  showDetails: boolean
}> = ({ item, showDetails }) => {
  const isFolder = item.type === 'folder'
  const isHidden = item.name.startsWith('.') || item.name.startsWith('_')

  return (
    <div className={cn(
      "flex items-center justify-between p-3 border border-border rounded-md hover:bg-accent/20 transition-colors",
      isHidden && "opacity-75"
    )}>
      <div className="flex items-center gap-3 min-w-0 flex-1">
        <span className="text-lg flex-shrink-0">
          {isFolder ? '📁' : getFileIcon(item.name)}
        </span>
        
        <div className="min-w-0 flex-1">
          <div className="font-medium truncate flex items-center gap-2">
            {item.name}
            {isHidden && (
              <Badge variant="outline" className="text-xs">
                Hidden
              </Badge>
            )}
          </div>
          {isFolder && item.files && (
            <div className="text-xs text-muted-foreground">
              {item.files.length} item{item.files.length !== 1 ? 's' : ''}
              {item.files.length === 0 && ' (empty)'}
            </div>
          )}
        </div>
      </div>

      {showDetails && (
        <div className="flex-shrink-0 text-xs text-muted-foreground">
          <Badge variant="outline" className="text-xs">
            {getFileCategory(item.name)}
          </Badge>
        </div>
      )}
    </div>
  )
}

/**
 * Get file icon based on extension
 */
function getFileIcon(filename: string): string {
  const extension = filename.split('.').pop()?.toLowerCase() || ''
  
  const icons: Record<string, string> = {
    'js': '🟨', 'jsx': '⚛️', 'ts': '🔷', 'tsx': '⚛️',
    'html': '🌐', 'css': '🎨', 'scss': '🎨', 'json': '📋',
    'md': '📝', 'txt': '📄', 'png': '🖼️', 'jpg': '🖼️',
    'pdf': '📕', 'zip': '📦', 'mp4': '🎬', 'mp3': '🎵',
    'env': '⚙️', 'gitignore': '🚫'
  }
  
  return icons[extension] || '📄'
}

/**
 * Get file category for display
 */
function getFileCategory(filename: string): string {
  const extension = filename.split('.').pop()?.toLowerCase() || ''
  
  const codeExts = ['js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs']
  const imageExts = ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'bmp', 'ico']
  const docExts = ['pdf', 'doc', 'docx', 'txt', 'md', 'rtf']
  const mediaExts = ['mp4', 'avi', 'mov', 'mp3', 'wav', 'flac']
  const configExts = ['json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'env']
  const archiveExts = ['zip', 'rar', '7z', 'tar', 'gz']
  
  if (codeExts.includes(extension)) return 'Code'
  if (imageExts.includes(extension)) return 'Image'
  if (docExts.includes(extension)) return 'Document'
  if (mediaExts.includes(extension)) return 'Media'
  if (configExts.includes(extension)) return 'Config'
  if (archiveExts.includes(extension)) return 'Archive'
  
  return 'File'
}

export default FilteringDemo
