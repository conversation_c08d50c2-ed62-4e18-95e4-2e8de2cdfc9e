/**
 * File Filtering Hook
 * React hook for integrating file filtering with components
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import { FileSystemItem } from '../types'
import { FileFilteringManager, FilterConfig, FilterCriteria, FilterCategory } from './FileFilteringManager'

export interface FileFilteringOptions {
  autoFilter?: boolean
  persistConfig?: boolean
  onFilterChange?: (config: FilterConfig) => void
}

export interface FileFilteringResult {
  filteredFiles: FileSystemItem[]
  filterConfig: FilterConfig
  filterCategories: FilterCategory[]
  filterStats: Record<FilterCriteria, number>
  toggleFilter: (filterId: FilterCriteria) => void
  updateFilterConfig: (config: Partial<FilterConfig>) => void
  resetFilters: () => void
  isLoading: boolean
  hasActiveFilters: boolean
}

export const useFileFiltering = (
  files: FileSystemItem[],
  options: FileFilteringOptions = {}
): FileFilteringResult => {
  const {
    autoFilter = true,
    persistConfig = true,
    onFilterChange
  } = options

  const [filterConfig, setFilterConfig] = useState<FilterConfig>({
    activeFilters: new Set(['all']),
    showHiddenFiles: false,
    showEmptyFolders: true,
    sizeFilter: {
      enabled: false,
      minSize: 0,
      maxSize: Number.MAX_SAFE_INTEGER
    },
    dateFilter: {
      enabled: false,
      startDate: null,
      endDate: null
    },
    namePattern: '',
    excludePatterns: []
  })
  const [isLoading, setIsLoading] = useState(false)
  const [manager] = useState(() => FileFilteringManager.getInstance())

  // Load saved configuration on mount
  useEffect(() => {
    if (persistConfig) {
      manager.loadFilterConfig()
      setFilterConfig(manager.getFilterConfig())
    }
  }, [manager, persistConfig])

  // Filter files when configuration or files change
  const filteredFiles = useMemo(() => {
    if (!autoFilter || files.length === 0) return files

    setIsLoading(true)
    
    try {
      // Update manager configuration
      manager.updateFilterConfig(filterConfig)
      
      // Filter files
      const filtered = manager.filterFiles(files)
      
      return filtered
    } catch (error) {
      console.error('Error filtering files:', error)
      return files
    } finally {
      setIsLoading(false)
    }
  }, [files, filterConfig, autoFilter, manager])

  // Get filter categories
  const filterCategories = useMemo(() => {
    return manager.getFilterCategories()
  }, [manager])

  // Get filter statistics
  const filterStats = useMemo(() => {
    return manager.getFilterStats(files)
  }, [files, manager])

  // Check if there are active filters (other than 'all')
  const hasActiveFilters = useMemo(() => {
    return !filterConfig.activeFilters.has('all') || 
           filterConfig.showHiddenFiles ||
           filterConfig.sizeFilter.enabled ||
           filterConfig.dateFilter.enabled ||
           filterConfig.namePattern.length > 0 ||
           filterConfig.excludePatterns.length > 0
  }, [filterConfig])

  /**
   * Toggle filter category
   */
  const toggleFilter = useCallback((filterId: FilterCriteria) => {
    manager.toggleFilter(filterId)
    const newConfig = manager.getFilterConfig()
    setFilterConfig(newConfig)
    onFilterChange?.(newConfig)
  }, [manager, onFilterChange])

  /**
   * Update filter configuration
   */
  const updateFilterConfig = useCallback((config: Partial<FilterConfig>) => {
    const newConfig = { ...filterConfig, ...config }
    setFilterConfig(newConfig)
    manager.updateFilterConfig(config)
    onFilterChange?.(newConfig)
  }, [filterConfig, manager, onFilterChange])

  /**
   * Reset all filters
   */
  const resetFilters = useCallback(() => {
    manager.resetFilters()
    const newConfig = manager.getFilterConfig()
    setFilterConfig(newConfig)
    onFilterChange?.(newConfig)
  }, [manager, onFilterChange])

  return {
    filteredFiles,
    filterConfig,
    filterCategories,
    filterStats,
    toggleFilter,
    updateFilterConfig,
    resetFilters,
    isLoading,
    hasActiveFilters
  }
}

/**
 * Hook for filter UI helpers
 */
export const useFilterIndicators = (filterConfig: FilterConfig, filterStats: Record<FilterCriteria, number>) => {
  /**
   * Check if filter is active
   */
  const isFilterActive = useCallback((filterId: FilterCriteria): boolean => {
    return filterConfig.activeFilters.has(filterId)
  }, [filterConfig.activeFilters])

  /**
   * Get filter count
   */
  const getFilterCount = useCallback((filterId: FilterCriteria): number => {
    return filterStats[filterId] || 0
  }, [filterStats])

  /**
   * Get CSS classes for filter button
   */
  const getFilterButtonClasses = useCallback((filterId: FilterCriteria): string => {
    const baseClasses = 'filter-button'
    const activeClasses = isFilterActive(filterId) ? 'active' : ''
    const emptyClasses = getFilterCount(filterId) === 0 ? 'empty' : ''
    return `${baseClasses} ${activeClasses} ${emptyClasses}`.trim()
  }, [isFilterActive, getFilterCount])

  /**
   * Format filter configuration for display
   */
  const formatFilterConfig = useCallback((): string => {
    const activeFilters = Array.from(filterConfig.activeFilters)
    if (activeFilters.includes('all')) {
      return 'All files'
    }
    
    if (activeFilters.length === 1) {
      return `${activeFilters[0]} files`
    }
    
    return `${activeFilters.length} filters active`
  }, [filterConfig.activeFilters])

  /**
   * Get active filter count
   */
  const getActiveFilterCount = useCallback((): number => {
    if (filterConfig.activeFilters.has('all')) return 0
    return filterConfig.activeFilters.size
  }, [filterConfig.activeFilters])

  return {
    isFilterActive,
    getFilterCount,
    getFilterButtonClasses,
    formatFilterConfig,
    getActiveFilterCount
  }
}

/**
 * Hook for advanced filtering features
 */
export const useAdvancedFiltering = (files: FileSystemItem[]) => {
  const [customFilters, setCustomFilters] = useState<Array<{
    id: string
    name: string
    pattern: string
    enabled: boolean
  }>>([])

  /**
   * Add custom filter
   */
  const addCustomFilter = useCallback((name: string, pattern: string) => {
    const newFilter = {
      id: `custom-${Date.now()}`,
      name,
      pattern,
      enabled: true
    }
    setCustomFilters(prev => [...prev, newFilter])
  }, [])

  /**
   * Remove custom filter
   */
  const removeCustomFilter = useCallback((id: string) => {
    setCustomFilters(prev => prev.filter(f => f.id !== id))
  }, [])

  /**
   * Toggle custom filter
   */
  const toggleCustomFilter = useCallback((id: string) => {
    setCustomFilters(prev => prev.map(f => 
      f.id === id ? { ...f, enabled: !f.enabled } : f
    ))
  }, [])

  /**
   * Get file type suggestions based on current files
   */
  const getFileTypeSuggestions = useCallback((): Array<{ extension: string; count: number }> => {
    const extensionCounts: Record<string, number> = {}
    
    const countExtensions = (items: FileSystemItem[]) => {
      items.forEach(item => {
        if (item.type !== 'folder') {
          const extension = item.name.includes('.') 
            ? item.name.split('.').pop()?.toLowerCase() || 'no-extension'
            : 'no-extension'
          extensionCounts[extension] = (extensionCounts[extension] || 0) + 1
        }
        
        if (item.files) {
          countExtensions(item.files)
        }
      })
    }
    
    countExtensions(files)
    
    return Object.entries(extensionCounts)
      .map(([extension, count]) => ({ extension, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
  }, [files])

  /**
   * Get size distribution
   */
  const getSizeDistribution = useCallback(() => {
    const sizes = { small: 0, medium: 0, large: 0, huge: 0 }
    
    const categorizeSize = (items: FileSystemItem[]) => {
      items.forEach(item => {
        if (item.type !== 'folder') {
          // Estimate file size (same logic as in manager)
          const size = item.name.length * 100 // Simplified estimation
          
          if (size < 10000) sizes.small++
          else if (size < 100000) sizes.medium++
          else if (size < 1000000) sizes.large++
          else sizes.huge++
        }
        
        if (item.files) {
          categorizeSize(item.files)
        }
      })
    }
    
    categorizeSize(files)
    return sizes
  }, [files])

  return {
    customFilters,
    addCustomFilter,
    removeCustomFilter,
    toggleCustomFilter,
    getFileTypeSuggestions,
    getSizeDistribution
  }
}

/**
 * Hook for filter presets
 */
export const useFilterPresets = () => {
  const [presets, setPresets] = useState<Array<{
    id: string
    name: string
    config: Partial<FilterConfig>
  }>>([
    {
      id: 'code-only',
      name: 'Code Files Only',
      config: {
        activeFilters: new Set(['code'])
      }
    },
    {
      id: 'recent-files',
      name: 'Recent Files',
      config: {
        dateFilter: {
          enabled: true,
          startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
          endDate: null
        }
      }
    },
    {
      id: 'large-files',
      name: 'Large Files',
      config: {
        sizeFilter: {
          enabled: true,
          minSize: 1000000, // 1MB
          maxSize: Number.MAX_SAFE_INTEGER
        }
      }
    }
  ])

  /**
   * Add custom preset
   */
  const addPreset = useCallback((name: string, config: Partial<FilterConfig>) => {
    const newPreset = {
      id: `preset-${Date.now()}`,
      name,
      config
    }
    setPresets(prev => [...prev, newPreset])
  }, [])

  /**
   * Remove preset
   */
  const removePreset = useCallback((id: string) => {
    setPresets(prev => prev.filter(p => p.id !== id))
  }, [])

  return {
    presets,
    addPreset,
    removePreset
  }
}
