/**
 * Filtering Controls Component
 * UI controls for file filtering with category buttons and advanced options
 */

import React, { useState } from 'react'
import { Filter, X, Settings, Eye, EyeOff, Calendar, HardDrive } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { FilterCriteria, FilterConfig, FilterCategory } from './FileFilteringManager'
import { useFilterIndicators } from './useFileFiltering'

interface FilteringControlsProps {
  filterConfig: FilterConfig
  filterCategories: FilterCategory[]
  filterStats: Record<FilterCriteria, number>
  onToggleFilter: (filterId: FilterCriteria) => void
  onUpdateConfig: (config: Partial<FilterConfig>) => void
  onResetFilters: () => void
  className?: string
  compact?: boolean
}

export const FilteringControls: React.FC<FilteringControlsProps> = ({
  filterConfig,
  filterCategories,
  filterStats,
  onToggleFilter,
  onUpdateConfig,
  onResetFilters,
  className,
  compact = false
}) => {
  const { isFilterActive, getFilterCount, formatFilterConfig, getActiveFilterCount } = useFilterIndicators(filterConfig, filterStats)
  const [showAdvanced, setShowAdvanced] = useState(false)

  if (compact) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn("h-8 gap-1", className)}
            title={formatFilterConfig()}
          >
            <Filter className="w-3.5 h-3.5" />
            {getActiveFilterCount() > 0 && (
              <Badge variant="secondary" className="h-4 px-1 text-xs">
                {getActiveFilterCount()}
              </Badge>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          <FilteringDropdownContent
            filterConfig={filterConfig}
            filterCategories={filterCategories}
            filterStats={filterStats}
            onToggleFilter={onToggleFilter}
            onUpdateConfig={onUpdateConfig}
            onResetFilters={onResetFilters}
            isFilterActive={isFilterActive}
            getFilterCount={getFilterCount}
          />
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Category Filters */}
      <div>
        <Label className="text-xs font-medium text-muted-foreground mb-2 block">
          File Types
        </Label>
        <div className="flex flex-wrap gap-1">
          {filterCategories.map((category) => (
            <FilterCategoryButton
              key={category.id}
              category={category}
              isActive={isFilterActive(category.id)}
              count={getFilterCount(category.id)}
              onClick={() => onToggleFilter(category.id)}
            />
          ))}
        </div>
      </div>

      {/* Quick Options */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Switch
            id="hidden-files"
            checked={filterConfig.showHiddenFiles}
            onCheckedChange={(checked) => onUpdateConfig({ showHiddenFiles: checked })}
          />
          <Label htmlFor="hidden-files" className="text-xs">
            Show hidden files
          </Label>
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="h-6 px-2 text-xs"
          >
            <Settings className="w-3 h-3 mr-1" />
            Advanced
          </Button>
          
          {getActiveFilterCount() > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onResetFilters}
              className="h-6 px-2 text-xs"
            >
              <X className="w-3 h-3 mr-1" />
              Reset
            </Button>
          )}
        </div>
      </div>

      {/* Advanced Options */}
      {showAdvanced && (
        <AdvancedFilterOptions
          filterConfig={filterConfig}
          onUpdateConfig={onUpdateConfig}
        />
      )}
    </div>
  )
}

/**
 * Filter Category Button Component
 */
const FilterCategoryButton: React.FC<{
  category: FilterCategory
  isActive: boolean
  count: number
  onClick: () => void
}> = ({ category, isActive, count, onClick }) => {
  return (
    <Button
      variant={isActive ? "default" : "outline"}
      size="sm"
      onClick={onClick}
      className={cn(
        "h-7 px-2 gap-1 text-xs",
        count === 0 && "opacity-50"
      )}
      title={`${category.description} (${count} files)`}
    >
      <span>{category.icon}</span>
      <span>{category.label}</span>
      <Badge variant="secondary" className="h-4 px-1 text-xs">
        {count}
      </Badge>
    </Button>
  )
}

/**
 * Advanced Filter Options Component
 */
const AdvancedFilterOptions: React.FC<{
  filterConfig: FilterConfig
  onUpdateConfig: (config: Partial<FilterConfig>) => void
}> = ({ filterConfig, onUpdateConfig }) => {
  return (
    <div className="space-y-3 p-3 border border-border rounded-md bg-muted/20">
      <Label className="text-xs font-medium">Advanced Filters</Label>
      
      {/* Name Pattern */}
      <div>
        <Label htmlFor="name-pattern" className="text-xs text-muted-foreground">
          Name Pattern
        </Label>
        <Input
          id="name-pattern"
          placeholder="*.js, test*, etc."
          value={filterConfig.namePattern}
          onChange={(e) => onUpdateConfig({ namePattern: e.target.value })}
          className="h-7 text-xs"
        />
      </div>

      {/* Size Filter */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Switch
            id="size-filter"
            checked={filterConfig.sizeFilter.enabled}
            onCheckedChange={(enabled) => 
              onUpdateConfig({ 
                sizeFilter: { ...filterConfig.sizeFilter, enabled } 
              })
            }
          />
          <Label htmlFor="size-filter" className="text-xs">
            Filter by size
          </Label>
        </div>
        
        {filterConfig.sizeFilter.enabled && (
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs text-muted-foreground">Min (KB)</Label>
              <Input
                type="number"
                placeholder="0"
                value={filterConfig.sizeFilter.minSize / 1024}
                onChange={(e) => 
                  onUpdateConfig({
                    sizeFilter: {
                      ...filterConfig.sizeFilter,
                      minSize: parseInt(e.target.value) * 1024 || 0
                    }
                  })
                }
                className="h-7 text-xs"
              />
            </div>
            <div>
              <Label className="text-xs text-muted-foreground">Max (KB)</Label>
              <Input
                type="number"
                placeholder="∞"
                value={filterConfig.sizeFilter.maxSize === Number.MAX_SAFE_INTEGER 
                  ? '' 
                  : filterConfig.sizeFilter.maxSize / 1024
                }
                onChange={(e) => 
                  onUpdateConfig({
                    sizeFilter: {
                      ...filterConfig.sizeFilter,
                      maxSize: parseInt(e.target.value) * 1024 || Number.MAX_SAFE_INTEGER
                    }
                  })
                }
                className="h-7 text-xs"
              />
            </div>
          </div>
        )}
      </div>

      {/* Other Options */}
      <div className="flex items-center gap-2">
        <Switch
          id="empty-folders"
          checked={filterConfig.showEmptyFolders}
          onCheckedChange={(checked) => onUpdateConfig({ showEmptyFolders: checked })}
        />
        <Label htmlFor="empty-folders" className="text-xs">
          Show empty folders
        </Label>
      </div>
    </div>
  )
}

/**
 * Dropdown content for compact filtering controls
 */
const FilteringDropdownContent: React.FC<{
  filterConfig: FilterConfig
  filterCategories: FilterCategory[]
  filterStats: Record<FilterCriteria, number>
  onToggleFilter: (filterId: FilterCriteria) => void
  onUpdateConfig: (config: Partial<FilterConfig>) => void
  onResetFilters: () => void
  isFilterActive: (filterId: FilterCriteria) => boolean
  getFilterCount: (filterId: FilterCriteria) => number
}> = ({
  filterConfig,
  filterCategories,
  onToggleFilter,
  onUpdateConfig,
  onResetFilters,
  isFilterActive,
  getFilterCount
}) => {
  return (
    <>
      <DropdownMenuLabel>Filter by Type</DropdownMenuLabel>
      {filterCategories.map((category) => (
        <DropdownMenuCheckboxItem
          key={category.id}
          checked={isFilterActive(category.id)}
          onCheckedChange={() => onToggleFilter(category.id)}
          className="flex items-center justify-between"
        >
          <div className="flex items-center gap-2">
            <span>{category.icon}</span>
            <span>{category.label}</span>
          </div>
          <Badge variant="outline" className="text-xs h-4">
            {getFilterCount(category.id)}
          </Badge>
        </DropdownMenuCheckboxItem>
      ))}
      
      <DropdownMenuSeparator />
      
      <DropdownMenuCheckboxItem
        checked={filterConfig.showHiddenFiles}
        onCheckedChange={(checked) => onUpdateConfig({ showHiddenFiles: checked })}
      >
        <Eye className="w-4 h-4 mr-2" />
        Show Hidden Files
      </DropdownMenuCheckboxItem>
      
      <DropdownMenuCheckboxItem
        checked={filterConfig.showEmptyFolders}
        onCheckedChange={(checked) => onUpdateConfig({ showEmptyFolders: checked })}
      >
        <Folder className="w-4 h-4 mr-2" />
        Show Empty Folders
      </DropdownMenuCheckboxItem>
      
      <DropdownMenuSeparator />
      
      <DropdownMenuItem onClick={onResetFilters}>
        <X className="w-4 h-4 mr-2" />
        Reset All Filters
      </DropdownMenuItem>
    </>
  )
}

/**
 * Filter Status Display Component
 */
interface FilterStatusProps {
  filterConfig: FilterConfig
  totalItems: number
  filteredItems: number
  className?: string
}

export const FilterStatus: React.FC<FilterStatusProps> = ({
  filterConfig,
  totalItems,
  filteredItems,
  className
}) => {
  const { formatFilterConfig, getActiveFilterCount } = useFilterIndicators(filterConfig, {})

  return (
    <div className={cn("flex items-center gap-2 text-xs text-muted-foreground", className)}>
      <Badge variant="outline" className="text-xs">
        {formatFilterConfig()}
      </Badge>
      <span>
        {filteredItems} of {totalItems} items
      </span>
      {getActiveFilterCount() > 0 && (
        <Badge variant="secondary" className="text-xs">
          {getActiveFilterCount()} filters
        </Badge>
      )}
    </div>
  )
}

/**
 * Quick Filter Buttons Component
 */
interface QuickFilterButtonsProps {
  onToggleFilter: (filterId: FilterCriteria) => void
  filterConfig: FilterConfig
  filterStats: Record<FilterCriteria, number>
  className?: string
}

export const QuickFilterButtons: React.FC<QuickFilterButtonsProps> = ({
  onToggleFilter,
  filterConfig,
  filterStats,
  className
}) => {
  const { isFilterActive } = useFilterIndicators(filterConfig, filterStats)

  const quickFilters: FilterCriteria[] = ['code', 'images', 'documents', 'folders']

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {quickFilters.map((filterId) => (
        <Button
          key={filterId}
          variant={isFilterActive(filterId) ? "default" : "ghost"}
          size="sm"
          onClick={() => onToggleFilter(filterId)}
          className="h-6 px-2 gap-1"
          title={`Filter ${filterId}`}
        >
          <span className="text-xs">
            {filterId === 'code' && '💻'}
            {filterId === 'images' && '🖼️'}
            {filterId === 'documents' && '📄'}
            {filterId === 'folders' && '📁'}
          </span>
          <span className="text-xs capitalize">{filterId}</span>
        </Button>
      ))}
    </div>
  )
}

export default FilteringControls
