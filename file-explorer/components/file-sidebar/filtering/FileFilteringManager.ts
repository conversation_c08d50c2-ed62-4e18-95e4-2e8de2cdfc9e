/**
 * File Filtering Manager
 * Handles comprehensive file filtering with multiple criteria and real-time updates
 */

import { FileSystemItem } from '../types'

export type FilterCriteria = 'all' | 'code' | 'images' | 'documents' | 'media' | 'config' | 'archives' | 'folders'

export interface FilterConfig {
  activeFilters: Set<FilterCriteria>
  showHiddenFiles: boolean
  showEmptyFolders: boolean
  sizeFilter: {
    enabled: boolean
    minSize: number // in bytes
    maxSize: number // in bytes
  }
  dateFilter: {
    enabled: boolean
    startDate: Date | null
    endDate: Date | null
  }
  namePattern: string
  excludePatterns: string[]
}

export interface FilterCategory {
  id: FilterCriteria
  label: string
  icon: string
  extensions: string[]
  description: string
  color: string
}

export class FileFilteringManager {
  private static instance: FileFilteringManager
  private currentConfig: FilterConfig = {
    activeFilters: new Set(['all']),
    showHiddenFiles: false,
    showEmptyFolders: true,
    sizeFilter: {
      enabled: false,
      minSize: 0,
      maxSize: Number.MAX_SAFE_INTEGER
    },
    dateFilter: {
      enabled: false,
      startDate: null,
      endDate: null
    },
    namePattern: '',
    excludePatterns: []
  }

  private filterCategories: FilterCategory[] = [
    {
      id: 'all',
      label: 'All Files',
      icon: '📁',
      extensions: [],
      description: 'Show all files and folders',
      color: 'blue'
    },
    {
      id: 'code',
      label: 'Code Files',
      icon: '💻',
      extensions: ['js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'clj', 'hs', 'elm'],
      description: 'Programming and script files',
      color: 'green'
    },
    {
      id: 'images',
      label: 'Images',
      icon: '🖼️',
      extensions: ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'bmp', 'ico', 'tiff', 'raw'],
      description: 'Image and graphic files',
      color: 'purple'
    },
    {
      id: 'documents',
      label: 'Documents',
      icon: '📄',
      extensions: ['pdf', 'doc', 'docx', 'txt', 'md', 'rtf', 'odt', 'pages'],
      description: 'Text documents and PDFs',
      color: 'orange'
    },
    {
      id: 'media',
      label: 'Media',
      icon: '🎬',
      extensions: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mp3', 'wav', 'flac', 'aac', 'm4a'],
      description: 'Audio and video files',
      color: 'red'
    },
    {
      id: 'config',
      label: 'Config',
      icon: '⚙️',
      extensions: ['json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf', 'env'],
      description: 'Configuration and data files',
      color: 'gray'
    },
    {
      id: 'archives',
      label: 'Archives',
      icon: '📦',
      extensions: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'],
      description: 'Compressed archive files',
      color: 'yellow'
    },
    {
      id: 'folders',
      label: 'Folders Only',
      icon: '📁',
      extensions: [],
      description: 'Show only folders',
      color: 'blue'
    }
  ]

  static getInstance(): FileFilteringManager {
    if (!FileFilteringManager.instance) {
      FileFilteringManager.instance = new FileFilteringManager()
    }
    return FileFilteringManager.instance
  }

  /**
   * Filter files according to current configuration
   */
  filterFiles(files: FileSystemItem[]): FileSystemItem[] {
    return files
      .map(file => this.filterItem(file))
      .filter(file => file !== null) as FileSystemItem[]
  }

  /**
   * Filter individual item
   */
  private filterItem(item: FileSystemItem): FileSystemItem | null {
    // Check if item passes all filters
    if (!this.passesFilters(item)) {
      return null
    }

    // If it's a folder, recursively filter children
    if (item.type === 'folder' && item.files) {
      const filteredChildren = this.filterFiles(item.files)
      
      // If folder is empty after filtering and we don't show empty folders
      if (!this.currentConfig.showEmptyFolders && filteredChildren.length === 0) {
        return null
      }

      return {
        ...item,
        files: filteredChildren
      }
    }

    return item
  }

  /**
   * Check if item passes all active filters
   */
  private passesFilters(item: FileSystemItem): boolean {
    // Hidden files filter
    if (!this.currentConfig.showHiddenFiles && this.isHiddenFile(item)) {
      return false
    }

    // Name pattern filter
    if (this.currentConfig.namePattern && !this.matchesNamePattern(item)) {
      return false
    }

    // Exclude patterns filter
    if (this.currentConfig.excludePatterns.length > 0 && this.matchesExcludePattern(item)) {
      return false
    }

    // Category filters
    if (!this.passesCategoryFilters(item)) {
      return false
    }

    // Size filter (only for files)
    if (item.type !== 'folder' && this.currentConfig.sizeFilter.enabled && !this.passesSizeFilter(item)) {
      return false
    }

    // Date filter
    if (this.currentConfig.dateFilter.enabled && !this.passesDateFilter(item)) {
      return false
    }

    return true
  }

  /**
   * Check if file is hidden
   */
  private isHiddenFile(item: FileSystemItem): boolean {
    return item.name.startsWith('.') || item.name.startsWith('_')
  }

  /**
   * Check if item matches name pattern
   */
  private matchesNamePattern(item: FileSystemItem): boolean {
    const pattern = this.currentConfig.namePattern.toLowerCase()
    const name = item.name.toLowerCase()
    
    // Support wildcards
    if (pattern.includes('*')) {
      const regexPattern = pattern.replace(/\*/g, '.*')
      const regex = new RegExp(regexPattern)
      return regex.test(name)
    }
    
    return name.includes(pattern)
  }

  /**
   * Check if item matches exclude patterns
   */
  private matchesExcludePattern(item: FileSystemItem): boolean {
    return this.currentConfig.excludePatterns.some(pattern => {
      const regexPattern = pattern.replace(/\*/g, '.*')
      const regex = new RegExp(regexPattern, 'i')
      return regex.test(item.name)
    })
  }

  /**
   * Check if item passes category filters
   */
  private passesCategoryFilters(item: FileSystemItem): boolean {
    // If 'all' is active, show everything
    if (this.currentConfig.activeFilters.has('all')) {
      return true
    }

    // If 'folders' filter is active and item is a folder
    if (this.currentConfig.activeFilters.has('folders') && item.type === 'folder') {
      return true
    }

    // For files, check if they match any active category
    if (item.type !== 'folder') {
      const extension = this.getFileExtension(item.name)
      
      for (const filterId of this.currentConfig.activeFilters) {
        const category = this.filterCategories.find(c => c.id === filterId)
        if (category && (category.extensions.includes(extension) || category.id === 'all')) {
          return true
        }
      }
    }

    return false
  }

  /**
   * Check if item passes size filter
   */
  private passesSizeFilter(item: FileSystemItem): boolean {
    const size = this.getFileSize(item)
    return size >= this.currentConfig.sizeFilter.minSize && 
           size <= this.currentConfig.sizeFilter.maxSize
  }

  /**
   * Check if item passes date filter
   */
  private passesDateFilter(item: FileSystemItem): boolean {
    const modifiedDate = this.getDateModified(item)
    
    if (this.currentConfig.dateFilter.startDate && modifiedDate < this.currentConfig.dateFilter.startDate) {
      return false
    }
    
    if (this.currentConfig.dateFilter.endDate && modifiedDate > this.currentConfig.dateFilter.endDate) {
      return false
    }
    
    return true
  }

  /**
   * Get file extension
   */
  private getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.')
    return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
  }

  /**
   * Get file size (realistic estimation)
   */
  private getFileSize(item: FileSystemItem): number {
    if (item.type === 'folder') return 0

    const extension = this.getFileExtension(item.name)
    const baseSize = item.name.length * 100
    
    const sizeMultipliers: Record<string, number> = {
      'js': 2, 'jsx': 2.5, 'ts': 2.2, 'tsx': 2.7, 'py': 1.8,
      'html': 3, 'css': 2, 'scss': 2.2, 'json': 1.5,
      'png': 100, 'jpg': 80, 'gif': 60, 'svg': 5,
      'mp4': 1000, 'mp3': 300, 'pdf': 50, 'zip': 200
    }

    const multiplier = sizeMultipliers[extension] || 1
    return Math.floor(baseSize * multiplier + this.hashString(item.name) % 1000)
  }

  /**
   * Get date modified (realistic estimation)
   */
  private getDateModified(item: FileSystemItem): Date {
    const now = new Date()
    const hash = this.hashString(item.name)
    const daysSinceModified = 1 + (hash % 60)
    
    return new Date(now.getTime() - (daysSinceModified * 24 * 60 * 60 * 1000))
  }

  /**
   * Simple string hash function
   */
  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash)
  }

  /**
   * Update filter configuration
   */
  updateFilterConfig(config: Partial<FilterConfig>): void {
    this.currentConfig = { ...this.currentConfig, ...config }
    this.saveFilterConfig()
  }

  /**
   * Toggle filter category
   */
  toggleFilter(filterId: FilterCriteria): void {
    const newFilters = new Set(this.currentConfig.activeFilters)
    
    if (filterId === 'all') {
      // If toggling 'all', clear other filters
      newFilters.clear()
      newFilters.add('all')
    } else {
      // Remove 'all' if adding specific filter
      newFilters.delete('all')
      
      if (newFilters.has(filterId)) {
        newFilters.delete(filterId)
      } else {
        newFilters.add(filterId)
      }
      
      // If no filters left, add 'all'
      if (newFilters.size === 0) {
        newFilters.add('all')
      }
    }
    
    this.currentConfig.activeFilters = newFilters
    this.saveFilterConfig()
  }

  /**
   * Get current filter configuration
   */
  getFilterConfig(): FilterConfig {
    return { ...this.currentConfig }
  }

  /**
   * Get filter categories
   */
  getFilterCategories(): FilterCategory[] {
    return [...this.filterCategories]
  }

  /**
   * Get filter statistics
   */
  getFilterStats(files: FileSystemItem[]): Record<FilterCriteria, number> {
    const stats: Record<FilterCriteria, number> = {} as any
    
    this.filterCategories.forEach(category => {
      stats[category.id] = this.countItemsInCategory(files, category.id)
    })
    
    return stats
  }

  /**
   * Count items in category
   */
  private countItemsInCategory(files: FileSystemItem[], categoryId: FilterCriteria): number {
    let count = 0
    
    const countRecursive = (items: FileSystemItem[]) => {
      items.forEach(item => {
        if (categoryId === 'all') {
          count++
        } else if (categoryId === 'folders' && item.type === 'folder') {
          count++
        } else if (item.type !== 'folder') {
          const extension = this.getFileExtension(item.name)
          const category = this.filterCategories.find(c => c.id === categoryId)
          if (category && category.extensions.includes(extension)) {
            count++
          }
        }
        
        if (item.files) {
          countRecursive(item.files)
        }
      })
    }
    
    countRecursive(files)
    return count
  }

  /**
   * Save filter configuration to localStorage
   */
  private saveFilterConfig(): void {
    try {
      const configToSave = {
        ...this.currentConfig,
        activeFilters: Array.from(this.currentConfig.activeFilters)
      }
      localStorage.setItem('fileFilterConfig', JSON.stringify(configToSave))
    } catch (error) {
      console.warn('Failed to save filter configuration:', error)
    }
  }

  /**
   * Load filter configuration from localStorage
   */
  loadFilterConfig(): void {
    try {
      const saved = localStorage.getItem('fileFilterConfig')
      if (saved) {
        const config = JSON.parse(saved)
        this.currentConfig = {
          ...this.currentConfig,
          ...config,
          activeFilters: new Set(config.activeFilters || ['all'])
        }
      }
    } catch (error) {
      console.warn('Failed to load filter configuration:', error)
    }
  }

  /**
   * Reset filters to default
   */
  resetFilters(): void {
    this.currentConfig = {
      activeFilters: new Set(['all']),
      showHiddenFiles: false,
      showEmptyFolders: true,
      sizeFilter: {
        enabled: false,
        minSize: 0,
        maxSize: Number.MAX_SAFE_INTEGER
      },
      dateFilter: {
        enabled: false,
        startDate: null,
        endDate: null
      },
      namePattern: '',
      excludePatterns: []
    }
    this.saveFilterConfig()
  }
}
