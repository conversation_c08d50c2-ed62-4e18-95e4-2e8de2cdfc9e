/**
 * File Sidebar Utilities
 * Utility functions for file operations, sorting, and path resolution
 */

import { FileSystemItem } from './types'

// Utility function to ensure unique IDs for file system items
export const ensureUniqueIds = (items: any[], parentPath: string = '', baseId: number = Date.now()): any[] => {
  return items.map((item, index) => {
    const uniqueId = `${baseId}-${parentPath}-${item.name}-${index}`.replace(/[^a-zA-Z0-9-]/g, '-');
    const processedItem = {
      ...item,
      id: uniqueId,
    };

    // Recursively process nested files if they exist
    if (item.files && Array.isArray(item.files)) {
      processedItem.files = ensureUniqueIds(item.files, `${parentPath}/${item.name}`, baseId);
    }

    return processedItem;
  });
};

// Filter files based on search query
export const filterFiles = (items: FileSystemItem[], query: string): FileSystemItem[] => {
  if (!query) return items

  return items.reduce((filtered: FileSystemItem[], item) => {
    if (item.type === "folder" || Array.isArray(item.files)) {
      const filteredFiles = filterFiles(item.files || [], query)
      if (filteredFiles.length > 0 || item.name.toLowerCase().includes(query.toLowerCase())) {
        filtered.push({
          ...item,
          expanded: filteredFiles.length > 0 ? true : item.expanded,
          files: filteredFiles,
        })
      }
    } else if (item.name.toLowerCase().includes(query.toLowerCase())) {
      filtered.push(item)
    }
    return filtered
  }, [])
}

// Check for unsaved changes in Monaco editor
export const checkForUnsavedChanges = async (): Promise<boolean> => {
  try {
    // Check if Monaco editor has unsaved changes
    if (typeof window !== 'undefined' && window.monaco) {
      const models = window.monaco.editor.getModels()
      for (const model of models) {
        if (model.isAttachedToEditor() && model.getValue() !== model.getAlternativeVersionId()) {
          return true // Has unsaved changes
        }
      }
    }
    return false
  } catch (error) {
    console.warn('Failed to check for unsaved changes:', error)
    return false
  }
}

// Save unsaved changes in Monaco editor
export const saveUnsavedChanges = async (): Promise<boolean> => {
  try {
    if (typeof window !== 'undefined' && window.monaco && window.electronAPI) {
      const models = window.monaco.editor.getModels()
      let saveCount = 0

      for (const model of models) {
        if (model.isAttachedToEditor()) {
          const uri = model.uri.toString()
          const content = model.getValue()

          // Extract file path from URI
          const filePath = uri.replace('file://', '')

          try {
            const result = await window.electronAPI.saveFile(filePath, content)
            if (result.success) {
              saveCount++
            } else {
              console.error('Failed to save file:', filePath, result.error)
            }
          } catch (saveError) {
            console.error('Error saving file:', filePath, saveError)
          }
        }
      }

      return saveCount > 0
    }
    return true
  } catch (error) {
    console.error('Failed to save unsaved changes:', error)
    return false
  }
}

// Load recent projects from settings
export const loadRecentProjects = async (): Promise<Array<{name: string, path: string, lastOpened: number}>> => {
  try {
    if (typeof window !== 'undefined' && window.electronAPI) {
      const { settingsManager } = await import('../settings/settings-manager')
      const recentProjectsList = await settingsManager.getRecentProjects()
      return recentProjectsList || []
    }
    return []
  } catch (error) {
    console.warn('Failed to load recent projects:', error)
    return []
  }
}

// Update recent projects list
export const updateRecentProjects = async (projectName: string, projectPath: string): Promise<void> => {
  try {
    if (typeof window !== 'undefined' && window.electronAPI) {
      const { settingsManager } = await import('../settings/settings-manager')
      await settingsManager.addRecentProject(projectName, projectPath)
    }
  } catch (error) {
    console.warn('Failed to update recent projects:', error)
  }
}

// Load project from file system path
export const loadProjectFromPath = async (projectPath: string, projectName: string): Promise<FileSystemItem | null> => {
  try {
    console.log("Loading project from path:", projectPath, projectName);

    // Ensure Taskmaster is initialized for this project
    try {
      const { claudeTaskmasterService } = await import('../../services/claude-taskmaster-service');
      const installCheck = await claudeTaskmasterService.checkInstallation();

      if (installCheck.installed) {
        console.log(`🔧 Auto-initializing Claude Taskmaster for existing project: ${projectName}`);
        const initResult = await claudeTaskmasterService.initializeProject(projectPath, projectName);

        if (initResult.success) {
          console.log(`✅ Taskmaster auto-initialized for ${projectName}`);
        } else {
          console.warn(`⚠️ Taskmaster auto-initialization failed: ${initResult.error}`);
        }
      } else {
        console.log(`✅ Taskmaster already initialized for ${projectName}`);
      }
    } catch (taskmasterError) {
      console.warn('⚠️ Taskmaster integration check failed:', taskmasterError);
    }

    // Set as active project for Agent System
    try {
      // Only set active project in Electron environment
      if (typeof window !== 'undefined' && window.electronAPI) {
        const { activeProjectService } = await import('../../services/active-project-service');
        activeProjectService.setActiveProject(projectPath, projectName);
        console.log("Set active project for agent system:", projectPath);

        // Register project with settings manager
        try {
          const { settingsManager } = await import('../settings/settings-manager');
          await settingsManager.createProject(projectName, projectPath);
          console.log("Registered opened project with settings manager");
        } catch (settingsError) {
          console.warn("Failed to register project with settings manager:", settingsError);
          // Continue anyway - project opening succeeded
        }
      }
    } catch (error) {
      console.warn("Failed to set active project:", error);
    }

    if (window.electronAPI) {
      console.log("Calling electronAPI.readDirectory");
      const result = await window.electronAPI.readDirectory(projectPath);
      console.log("readDirectory result:", result);

      if (result.success && result.items) {
        console.log("Successfully read directory, creating project structure");
        const baseId = Date.now();
        const newProject: FileSystemItem = {
          id: baseId,
          name: projectName,
          type: "folder",
          path: projectPath,
          expanded: true,
          files: ensureUniqueIds(await Promise.all(result.items.map(async (item: any) => {
            if (item.type === 'folder') {
              return {
                ...item,
                files: [] // We'll load subdirectories on demand
              };
            }
            return item;
          })), projectPath, baseId)
        };

        console.log("New project structure:", newProject);
        return newProject;
      } else {
        console.error("Failed to read directory:", result.error);
        throw new Error(`Failed to read directory: ${result.error || 'Unknown error'}`);
      }
    } else {
      console.log("🌐 File Explorer: electronAPI not available in browser environment");
      throw new Error('Failed to load project: Desktop app features not available in browser mode');
    }
  } catch (error) {
    console.error('Error loading project:', error);
    throw error;
  }
}

// Validate active project for PRD dialog
export const validateActiveProjectForPRD = async (): Promise<boolean> => {
  try {
    const { activeProjectService } = await import('../../services/active-project-service');
    const activeProject = activeProjectService.getActiveProject();

    if (!activeProject?.path) {
      console.error('❌ PRD Dialog blocked: No active project in global context');
      return false;
    }

    console.log(`✅ PRD Dialog validation passed: Active project ${activeProject.name} (${activeProject.path})`);
    return true;
  } catch (error) {
    console.error('❌ PRD Dialog validation failed:', error);
    return false;
  }
}

// Get file extension from filename
export const getFileExtension = (filename: string): string => {
  const parts = filename.split('.');
  return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
}

// Sort files based on criteria
export const sortFiles = (files: FileSystemItem[], sortBy: 'name' | 'modified' | 'size' | 'type', sortOrder: 'asc' | 'desc'): FileSystemItem[] => {
  return [...files].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'modified':
        const aTime = a.modified?.getTime() || 0;
        const bTime = b.modified?.getTime() || 0;
        comparison = aTime - bTime;
        break;
      case 'size':
        comparison = (a.size || 0) - (b.size || 0);
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
    }
    
    return sortOrder === 'asc' ? comparison : -comparison;
  });
}

/**
 * Count total files in a project or folder recursively
 */
export const countFiles = (item: FileSystemItem): number => {
  if (!item.files || !Array.isArray(item.files)) {
    return item.type === 'folder' ? 0 : 1
  }

  return item.files.reduce((count, file) => {
    if (file.type === 'folder') {
      return count + countFiles(file)
    } else {
      return count + 1
    }
  }, 0)
}

/**
 * Count total files across all projects
 */
export const countAllFiles = (projects: FileSystemItem[]): number => {
  return projects.reduce((total, project) => {
    return total + countFiles(project)
  }, 0)
}

/**
 * Count total folders in a project or folder recursively
 */
export const countFolders = (item: FileSystemItem): number => {
  if (!item.files || !Array.isArray(item.files)) {
    return item.type === 'folder' ? 1 : 0
  }

  return item.files.reduce((count, file) => {
    if (file.type === 'folder') {
      return count + 1 + countFolders(file)
    } else {
      return count
    }
  }, item.type === 'folder' ? 1 : 0)
}

/**
 * Get file statistics for projects
 */
export const getFileStatistics = (projects: FileSystemItem[]) => {
  const totalFiles = countAllFiles(projects)
  const totalFolders = projects.reduce((total, project) => total + countFolders(project), 0)

  return {
    totalFiles,
    totalFolders,
    totalProjects: projects.length
  }
}
