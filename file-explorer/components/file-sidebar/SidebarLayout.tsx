/**
 * Sidebar Layout Component
 * Renders the main structure: headers, search, and sections with error tracking
 */

import React from "react"
import { Search, Settings, Plus, FolderPlus, Play, AlertCircle, AlertTriangle } from "lucide-react"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { SidebarLayoutProps } from "./types"
import { useErrorTracking, useErrorStatistics } from "./error-indicators/useErrorTracking"
import { getFileStatistics } from "./sidebar-utils"

export const SidebarLayout = ({
  searchQuery,
  onSearchChange,
  onCreateProject,
  onOpenProject,
  onStartOrchestration,
  onShowSettings,
  projectCount,
  hasProjects,
  projects,
  children
}: SidebarLayoutProps) => {
  // Initialize error tracking
  const errorTracking = useErrorTracking({
    enableMockData: process.env.NODE_ENV === 'development',
    autoStart: true
  })

  // Get error statistics for display
  const errorStats = useErrorStatistics()

  // Calculate real file statistics
  const fileStats = getFileStatistics(projects)
  return (
    <div className="h-full bg-editor-sidebar-bg text-editor-sidebar-fg flex flex-col">
      {/* Header Section */}
      <div className="p-3 border-b border-editor-border">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-medium truncate">Explorer</h2>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-muted-foreground hover:text-foreground"
              onClick={onOpenProject}
              title="Open existing project"
            >
              <FolderPlus className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-muted-foreground hover:text-foreground"
              onClick={onCreateProject}
              title="Create new project"
            >
              <Plus className="h-3.5 w-3.5" />
            </Button>
            {hasProjects && (
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-muted-foreground hover:text-foreground"
                onClick={onStartOrchestration}
                title="Orchestrate Taskmaster tasks"
              >
                <Play className="h-3.5 w-3.5" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-muted-foreground hover:text-foreground"
              onClick={onShowSettings}
              title="Explorer settings"
            >
              <Settings className="h-3.5 w-3.5" />
            </Button>
          </div>
        </div>

        {/* Search Section */}
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-3.5 w-3.5 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            className="pl-8 h-9 bg-background border-input text-sm"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
      </div>

      {/* Main Content Area */}
      <ScrollArea className="flex-1">
        {children}
      </ScrollArea>

      {/* Footer Section */}
      <div className="p-3 border-t border-editor-border">
        <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
          <span>{projectCount} projects</span>
          <span>{fileStats.totalFiles} files</span>
        </div>

        {/* Error Statistics */}
        {(errorStats.totalErrors > 0 || errorStats.totalWarnings > 0) && (
          <div className="flex items-center gap-3 text-xs">
            {errorStats.totalErrors > 0 && (
              <div className="flex items-center gap-1 text-red-500">
                <AlertCircle className="h-3 w-3" />
                <span>{errorStats.totalErrors} error{errorStats.totalErrors !== 1 ? 's' : ''}</span>
              </div>
            )}
            {errorStats.totalWarnings > 0 && (
              <div className="flex items-center gap-1 text-yellow-500">
                <AlertTriangle className="h-3 w-3" />
                <span>{errorStats.totalWarnings} warning{errorStats.totalWarnings !== 1 ? 's' : ''}</span>
              </div>
            )}
            <div className="ml-auto text-muted-foreground">
              {errorStats.filesWithErrors} file{errorStats.filesWithErrors !== 1 ? 's' : ''} affected
            </div>
          </div>
        )}

        {/* Error Tracking Status */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-1 text-xs text-muted-foreground">
            Error tracking: {errorTracking.isTracking ? '🟢 Active' : '🔴 Inactive'}
            {errorTracking.lastUpdate > 0 && (
              <span className="ml-2">
                Last update: {new Date(errorTracking.lastUpdate).toLocaleTimeString()}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
