/**
 * Sidebar Layout Component
 * Renders the main structure: headers, search, and sections with error tracking
 */

import React from "react"
import { Search, Settings, Plus, FolderPlus, Play, AlertCircle, AlertTriangle } from "lucide-react"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { SidebarLayoutProps } from "./types"
import { useErrorTracking, useErrorStatistics } from "./error-indicators/useErrorTracking"
import { getFileStatistics } from "./sidebar-utils"
import { FileSearch } from "./search/FileSearch"
import { PreviewPanel, PreviewToggle } from "./preview/PreviewPanel"
import { useKeyboardNavigation } from "./keyboard/useKeyboardNavigation"
import { KeyboardShortcutsButton, KeyboardShortcutsHelp, NavigationStatus } from "./keyboard/KeyboardShortcutsHelp"
import { useFileSorting } from "./sorting/useFileSorting"
import { SortingControls, SortStatus } from "./sorting/SortingControls"
import { useFileFiltering } from "./filtering/useFileFiltering"
import { FilteringControls, FilterStatus } from "./filtering/FilteringControls"
import { RecentFilesSection } from "./recent/RecentFilesSection"
import { useRecentFilesTracking } from "./recent/useRecentFiles"
import { OperationsFeedback, UndoButton } from "./operations/OperationsFeedback"
import { useFileOperations } from "./operations/useFileOperations"

export const SidebarLayout = ({
  searchQuery,
  onSearchChange,
  onCreateProject,
  onOpenProject,
  onStartOrchestration,
  onShowSettings,
  onFileSelect,
  projectCount,
  hasProjects,
  projects,
  children
}: SidebarLayoutProps) => {
  const [showPreview, setShowPreview] = React.useState(true)
  const [selectedFile, setSelectedFile] = React.useState<FileSystemItem | null>(null)
  const [showKeyboardHelp, setShowKeyboardHelp] = React.useState(false)
  const [showRecentFiles, setShowRecentFiles] = React.useState(true)

  // Recent files tracking
  const recentFilesTracking = useRecentFilesTracking()

  // File operations
  const fileOperations = useFileOperations({
    autoCleanup: true,
    maxDisplayOperations: 10,
    onOperationComplete: (operation) => {
      console.log('Operation completed:', operation)
    },
    onOperationFailed: (operation) => {
      console.error('Operation failed:', operation)
    }
  })

  // File filtering
  const fileFiltering = useFileFiltering(projects, {
    autoFilter: true,
    persistConfig: true,
    onFilterChange: (config) => {
      console.log('Filter configuration changed:', config)
    }
  })

  // File sorting (applied to filtered files)
  const fileSorting = useFileSorting(fileFiltering.filteredFiles, {
    autoSort: true,
    persistConfig: true,
    onSortChange: (config) => {
      console.log('Sort configuration changed:', config)
    }
  })

  // Keyboard navigation (using sorted files)
  const keyboardNav = useKeyboardNavigation(fileSorting.sortedFiles, {
    onFileSelect: (file) => {
      setSelectedFile(file)
      onFileSelect(file)
    },
    onFolderToggle: (folder) => {
      // This would be handled by the parent component
      console.log('Toggle folder:', folder.name)
    },
    onSearchFocus: () => {
      // Focus search input
      const searchInput = document.querySelector('input[placeholder*="Search"]') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
      }
    },
    enabled: true
  })
  // Initialize error tracking
  const errorTracking = useErrorTracking({
    enableMockData: process.env.NODE_ENV === 'development',
    autoStart: true
  })

  // Get error statistics for display
  const errorStats = useErrorStatistics()

  // Calculate real file statistics
  const fileStats = getFileStatistics(projects)
  return (
    <div
      className="h-full bg-editor-sidebar-bg text-editor-sidebar-fg flex flex-col"
      data-keyboard-nav-container
      tabIndex={0}
      onKeyDown={keyboardNav.handleKeyDown}
      onFocus={() => keyboardNav.focusExplorer()}
    >
      {/* Header Section */}
      <div className="px-3 py-4 border-b border-editor-border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-base font-semibold text-foreground truncate leading-6">Explorer</h2>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-muted-foreground hover:text-foreground transition-colors duration-150"
              onClick={onOpenProject}
              title="Open existing project"
            >
              <FolderPlus className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-muted-foreground hover:text-foreground transition-colors duration-150"
              onClick={onCreateProject}
              title="Create new project"
            >
              <Plus className="h-3.5 w-3.5" />
            </Button>
            {hasProjects && (
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-muted-foreground hover:text-foreground transition-colors duration-150"
                onClick={onStartOrchestration}
                title="Orchestrate Taskmaster tasks"
              >
                <Play className="h-3.5 w-3.5" />
              </Button>
            )}
            <UndoButton
              canUndo={fileOperations.canUndo}
              onUndo={fileOperations.undoLastOperation}
              className="h-7 text-xs px-2"
            />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowRecentFiles(!showRecentFiles)}
              className="h-7 w-7 text-muted-foreground hover:text-foreground transition-colors duration-150"
              title={showRecentFiles ? "Hide Recent Files" : "Show Recent Files"}
            >
              <Clock className="h-3.5 w-3.5" />
            </Button>
            <PreviewToggle
              isVisible={showPreview}
              onToggle={() => setShowPreview(!showPreview)}
              className="h-7 w-7 text-muted-foreground hover:text-foreground transition-colors duration-150"
            />
            <FilteringControls
              filterConfig={fileFiltering.filterConfig}
              filterCategories={fileFiltering.filterCategories}
              filterStats={fileFiltering.filterStats}
              onToggleFilter={fileFiltering.toggleFilter}
              onUpdateConfig={fileFiltering.updateFilterConfig}
              onResetFilters={fileFiltering.resetFilters}
              compact={true}
              className="h-7"
            />
            <SortingControls
              sortConfig={fileSorting.sortConfig}
              onSortChange={fileSorting.setSortCriteria}
              onDirectionChange={fileSorting.setSortDirection}
              onConfigChange={fileSorting.updateSortConfig}
              availableCriteria={fileSorting.availableCriteria}
              compact={true}
              className="h-7"
            />
            <KeyboardShortcutsButton
              onClick={() => setShowKeyboardHelp(true)}
              className="h-7 w-7 text-muted-foreground hover:text-foreground transition-colors duration-150"
            />
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-muted-foreground hover:text-foreground transition-colors duration-150"
              onClick={onShowSettings}
              title="Explorer settings"
            >
              <Settings className="h-3.5 w-3.5" />
            </Button>
          </div>
        </div>

        {/* Enhanced Search Section */}
        <FileSearch
          projects={projects}
          onFileSelect={(file) => {
            setSelectedFile(file)
            onFileSelect(file)
            recentFilesTracking.trackFileView(file, file.path)
          }}
          placeholder="Search files..."
          maxResults={50}
          minScore={0.1}
        />
      </div>

      {/* Recent Files Section */}
      {showRecentFiles && (
        <div className="px-3 py-2 border-b border-editor-border">
          <RecentFilesSection
            onFileSelect={(file) => {
              setSelectedFile(file)
              onFileSelect(file)
              recentFilesTracking.trackFileView(file, file.path)
            }}
            onFileOpen={(file) => {
              setSelectedFile(file)
              onFileSelect(file)
              recentFilesTracking.trackFileOpen(file, file.path)
            }}
            maxItems={10}
            showProjectGroups={false}
            showTimeGroups={true}
          />
        </div>
      )}

      {/* Operations Feedback Section */}
      {(fileOperations.activeOperations.length > 0 || fileOperations.operations.length > 0) && (
        <div className="px-3 py-2 border-b border-editor-border">
          <OperationsFeedback
            compact={true}
            showCompleted={false}
            maxItems={3}
          />
        </div>
      )}

      {/* Main Content Area */}
      <ScrollArea className="flex-1 file-explorer-scroll">
        {children(fileSorting.sortedFiles)}
      </ScrollArea>

      {/* Preview Panel */}
      {showPreview && (
        <PreviewPanel
          selectedFile={selectedFile}
          onClose={() => setShowPreview(false)}
          collapsible={true}
        />
      )}

      {/* Footer Section */}
      <div className="px-3 py-3 border-t border-editor-border bg-editor-sidebar-bg/50">
        <div className="flex items-center justify-between file-explorer-footer mb-2">
          <span>{projectCount} projects</span>
          <span>{fileStats.totalFiles} files</span>
        </div>

        {/* Error Statistics */}
        {(errorStats.totalErrors > 0 || errorStats.totalWarnings > 0) && (
          <div className="flex items-center gap-3 text-xs leading-4 mb-2">
            {errorStats.totalErrors > 0 && (
              <div className="flex items-center gap-1.5 text-red-500">
                <AlertCircle className="h-3 w-3 flex-shrink-0" />
                <span className="font-medium">{errorStats.totalErrors} error{errorStats.totalErrors !== 1 ? 's' : ''}</span>
              </div>
            )}
            {errorStats.totalWarnings > 0 && (
              <div className="flex items-center gap-1.5 text-yellow-500">
                <AlertTriangle className="h-3 w-3 flex-shrink-0" />
                <span className="font-medium">{errorStats.totalWarnings} warning{errorStats.totalWarnings !== 1 ? 's' : ''}</span>
              </div>
            )}
            <div className="ml-auto text-muted-foreground/70 text-xs">
              {errorStats.filesWithErrors} file{errorStats.filesWithErrors !== 1 ? 's' : ''} affected
            </div>
          </div>
        )}

        {/* Error Tracking Status */}
        {process.env.NODE_ENV === 'development' && (
          <div className="text-xs text-muted-foreground/60 leading-4 font-mono">
            Error tracking: {errorTracking.isTracking ? '🟢 Active' : '🔴 Inactive'}
            {errorTracking.lastUpdate > 0 && (
              <span className="ml-2">
                Last update: {new Date(errorTracking.lastUpdate).toLocaleTimeString()}
              </span>
            )}
          </div>
        )}

        {/* Filter Status */}
        <FilterStatus
          filterConfig={fileFiltering.filterConfig}
          totalItems={projects.length}
          filteredItems={fileFiltering.filteredFiles.length}
          className="px-3 py-1 border-t border-editor-border"
        />

        {/* Sort Status */}
        <SortStatus
          sortConfig={fileSorting.sortConfig}
          totalItems={fileFiltering.filteredFiles.length}
          className="px-3 py-1 border-t border-editor-border"
        />

        {/* Keyboard Navigation Status */}
        <NavigationStatus
          isActive={keyboardNav.selectedItem !== null}
          selectedItem={keyboardNav.selectedItem?.name || null}
          className="px-3 py-1 border-t border-editor-border"
        />
      </div>

      {/* Keyboard Shortcuts Help */}
      <KeyboardShortcutsHelp
        isOpen={showKeyboardHelp}
        onClose={() => setShowKeyboardHelp(false)}
      />
    </div>
  )
}
